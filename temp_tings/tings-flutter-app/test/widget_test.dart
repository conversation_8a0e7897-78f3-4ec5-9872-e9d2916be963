// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:tings/main.dart';
import 'package:tings/widgets/product/egift/utils/data_format.dart';

void main() {
  String originalData = '{"key":"value"}'; // Example JSON data

  // Encrypt and encode
  String encryptedData = urlEncodeData(originalData);
  print("Encrypted and Encoded: $encryptedData");

  // Decode and decrypt
  String decryptedData = urlDecodeData(encryptedData);
  print("Decrypted Data: $decryptedData");

  // Verify if the decrypted data matches the original data
  assert(decryptedData == originalData);
}
