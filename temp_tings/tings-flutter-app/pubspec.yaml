name: tings
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.5.1+1

environment:
  sdk: '>=2.18.2 <3.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  animations: ^2.0.7
  flutter_staggered_grid_view: ^0.6.2
  http: 1.1.0
  transparent_image: ^2.0.0
  carousel_slider: ^4.1.1
  smooth_page_indicator: ^1.0.0+2
  google_fonts: 6.1.0
  shared_preferences: ^2.0.15
  substring_highlight: ^1.0.33
  firebase_auth: 4.10.1
  firebase_core: 2.17.0
  google_sign_in: 6.1.5
  flutter_facebook_auth: 6.0.2
  sign_in_with_apple: 5.0.0
  crypto: ^3.0.2
  image_picker: ^0.8.6
  url_launcher: ^6.2.3
  uuid: 3.0.6
  textfield_datepicker: ^0.1.0
  intl: ^0.17.0
  webview_flutter: 4.0.2
  cached_network_image: ^3.3.0
  ensure_visible_when_focused: ^1.1.1
  humanize_duration: 0.0.1+1
  receive_sharing_intent: ^1.4.5
  debounce_throttle: ^2.0.0
  package_info_plus: ^4.2.0
  mixpanel_flutter: ^2.0.0
  flutter_animate: ^4.3.0
  share_plus: ^4.5.3
  # encrypt: ^5.0.3
  path_provider: ^2.1.2
  go_router: ^13.0.1
  uni_links: ^0.5.1
  flutter_bloc: ^8.1.3
  gif: ^2.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - images/iconly/Bold/Category.png
    - images/iconly/Light/Category.png
    - images/iconly/Bold/Heart.png
    - images/iconly/Light/Heart.png
    - images/iconly/Bold/Search.png
    - images/iconly/Light/Search.png
    - images/iconly/Bold/3_User.png
    - images/iconly/Light/3_User.png
    - images/iconly/Light/3_User_yellow.png
    - images/iconly/Light/Add_User_yellow.png
    - images/iconly/Light/ArrowLeft2.png
    - images/iconly/Light/ArrowRight2.png
    - images/iconly/Light/Calendar_small.png
    - images/iconly/Bold/Profile.png
    - images/iconly/Light/Profile.png
    - images/iconly/Light/Info_Circle_red.png
    - images/iconly/Light/Calendar.png
    - images/iconly/Light/More_Circle.png
    - images/iconly/Light/Notification.png
    - images/iconly/Light/Document.png
    - images/icons/Filter.png
    - images/icons/Upload.png
    - images/icons/add-circle-yellow.png
    - images/icons/question-circle.png
    - images/icons/radio.png
    - images/icons/radio_filled.png
    - images/icons/checkbox.png
    - images/icons/checkbox_filled.png
    - images/icons/Search_grey.png
    - images/icons/x-circle.png
    - images/icons/btn_google.png
    - images/icons/icon_facebook.png
    - images/icons/logos_apple.png
    - images/iconly/Light/Info_Circle.png
    - images/icons/lock.png
    - images/icons/Lock_small.png
    - images/icons/Lock_yellow.png
    - images/icons/Calendar_yellow.png
    - images/icons/clear.png
    - images/icons/add-circle.png
    - images/icons/cog.png
    - images/icons/checkmark.png
    - images/icons/edit.png
    - images/logo.png
    - images/no-image-found.png
    - images/welcome_back.png
    - images/btn_mytings.png
    - images/Rectangle_80.png
    - images/Rectangle_79.png
    - images/wishlist_background.png
    - images/arrow_back_yellow.png
    - images/bag_face.png
    - images/tings_place.png
    - images/progress_check.png
    - images/progress_uncheck.png
    - images/product_placeholder.png
    - images/birthday_card.png
    - images/giftbox.png
    - images/gift.png
    - images/starbucks_logo.png
    - images/venmo_logo.png
    - images/zelle_logo.png
    - images/cashapp_logo.png
    - images/sender_confirmation.gif
    - images/unwrapping.gif
    - images/google_pay.png 
    - images/app_wallet.png

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
