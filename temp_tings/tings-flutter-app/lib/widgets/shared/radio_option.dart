import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class RadioOption<T> extends StatelessWidget {
  final T value;
  final T? groupValue;
  final String text;
  final ValueChanged<T?> onChanged;

  const RadioOption({
    super.key,
    required this.value,
    required this.groupValue,
    required this.text,
    required this.onChanged,
  });

  Widget _buildLabel() {
    final bool isSelected = value == groupValue;
    final String imgUrl =
        isSelected ? 'images/icons/radio_filled.png' : 'images/icons/radio.png';
    return Image.asset(
      imgUrl,
      height: 18,
      width: 18,
    );
  }

  Widget _buildText() {
    final bool isSelected = value == groupValue;
    return Text(text,
        style: GoogleFonts.inter(
            textStyle: TextStyle(
                fontSize: 14,
                color: Colors.black,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400)));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: InkWell(
        onTap: () => onChanged(value),
        splashColor: Colors.cyan.withOpacity(0.5),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 7),
          child: Row(
            children: [
              _buildLabel(),
              const SizedBox(width: 10),
              _buildText(),
            ],
          ),
        ),
      ),
    );
  }
}
