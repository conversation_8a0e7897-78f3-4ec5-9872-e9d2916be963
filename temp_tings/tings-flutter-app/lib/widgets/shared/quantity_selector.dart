import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/shared/conditional.dart';

class QuantitySelector extends StatelessWidget {
  final String? title;
  final int value;
  final Function(int) onChange;

  const QuantitySelector(
      {super.key, required this.value, required this.onChange, this.title});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Conditional(
            condition: title != null,
            child: Padding(
              padding: const EdgeInsets.only(right: 19),
              child: Text(title ?? '',
                  style: GoogleFonts.inter(
                      textStyle:
                          const TextStyle(fontSize: 16, color: Colors.black))),
            )),
        SizedBox(
          width: 119,
          height: 46,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(40)),
                border: Border.all(
                    color: const Color(0xFFBCBCBC),
                    width: 1,
                    style: BorderStyle.solid)),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  IconButton(
                    onPressed: () => onChange(value - 1),
                    icon: const Icon(Icons.remove, size: 16),
                    color: Colors.black,
                  ),
                  Text(value.toString(),
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 18,
                              color: Colors.black,
                              fontWeight: FontWeight.w600))),
                  IconButton(
                    onPressed: () => onChange(value + 1),
                    icon: const Icon(Icons.add, size: 16),
                    color: Colors.black,
                  )
                ],
              ),
            ),
          ),
        )
      ],
    );
  }
}
