import 'package:flutter/material.dart';

class Conditional extends StatelessWidget {
  final Widget child;
  final Widget? alternate;
  final bool condition;

  const Conditional(
      {super.key,
      required this.condition,
      this.alternate,
      required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: condition ? child : alternate ?? const SizedBox(),
    );
  }
}
