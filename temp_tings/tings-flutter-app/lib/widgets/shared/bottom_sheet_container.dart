import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class BottomSheetContainer extends StatelessWidget {
  final List<Widget> children;
  final String title;
  final Function(bool) onClose;
  final double? height;
  final double? marginTop;
  final String? headerBackgroundImage;
  final Widget? closeIcon;
  final CrossAxisAlignment? crossAxisAlignment;

  const BottomSheetContainer(
      {super.key,
      required this.children,
      required this.title,
      required this.onClose,
      this.closeIcon,
      this.height,
      this.marginTop,
      this.crossAxisAlignment,
      this.headerBackgroundImage});

  @override
  Widget build(BuildContext context) {
    return Container(
        height: height,
        margin: EdgeInsets.only(top: marginTop ?? 70),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.vertical(
              top: Radius.circular(20), bottom: Radius.zero),
          color: Colors.white,
        ),
        clipBehavior: Clip.hardEdge,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: crossAxisAlignment ?? CrossAxisAlignment.center,
          children: [
              Container(
                decoration: BoxDecoration(
                    image: headerBackgroundImage != null
                        ? DecorationImage(
                            image: AssetImage(headerBackgroundImage!),
                            fit: BoxFit.cover)
                        : null),
                child: SizedBox(
                  height: 60,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(
                        width: 40,
                      ),
                      Expanded(
                          child: Text(
                        title,
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                                fontSize: 20,
                                color: Colors.black,
                                fontWeight: FontWeight.w700)),
                        textAlign: TextAlign.center,
                      )),
                      IconButton(
                        onPressed: () {
                          onClose(false);
                        },
                        icon: closeIcon ?? Image.asset(
                          "images/icons/clear.png",
                          width: 18,
                          height: 18,
                        ),
                        color: Colors.black,
                      )
                    ],
                  ),
                ),
              ),
              ...children,
            const SizedBox(
              height: 40,
            ),
            ]));
  }
}
