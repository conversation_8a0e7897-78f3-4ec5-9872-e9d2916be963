import 'package:mixpanel_flutter/mixpanel_flutter.dart';
import 'package:tings/globals.dart';

class EventTracker {
  late Mixpanel mixpanel;

  static final EventTracker _instance = EventTracker._internal();

  // using a factory is important
  // because it promises to return _an_ object of this type
  // but it doesn't promise to make a new one.
  factory EventTracker() {
    return _instance;
  }

  // This named constructor is the "real" constructor
  // It'll be called exactly once, by the static property assignment above
  // it's also private, so it can only be called in this class
  EventTracker._internal() {
    // initialization logic
  }

  Future<Mixpanel> getMixpanel() async {
    // Replace with your Project Token
    // Once you've called this method once, you can access `mixpanel` throughout the rest of your application.
    mixpanel = await Mixpanel.init("2d14e030c2eb0c89c835be66ec4c078a", trackAutomaticEvents: true);
    return mixpanel;
  }

  Future track(String name, {Map<String, dynamic>? properties}) async {
    print('Track event: {name: $name, properties: ${properties.toString()}}');
    
    if (isProduction) {
      var mix = await getMixpanel();
      mix.track(name, properties: properties);
    }
  }

}