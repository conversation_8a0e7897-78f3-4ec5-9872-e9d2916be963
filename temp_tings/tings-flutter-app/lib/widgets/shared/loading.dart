import 'package:flutter/material.dart';

class Loading extends StatelessWidget {
  final bool loading;
  final double? strokeWidth;
  final EdgeInsets? padding;

  const Loading({super.key, required this.loading, this.padding, this.strokeWidth});

  @override
  Widget build(BuildContext context) {
    if (loading) {
      return Center(
        child: Container(
          padding: padding ?? const EdgeInsets.all(8),
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(50)),
            color: Colors.white,
          ),
          child: CircularProgressIndicator(strokeWidth: strokeWidth ?? 4.0),
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }
}
