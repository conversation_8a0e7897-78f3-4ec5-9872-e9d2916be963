import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CheckboxOption extends StatelessWidget {

  final bool value;
  final String text;
  final Function(bool) onChanged;

  const CheckboxOption({
    required this.value,
    required this.text,
    required this.onChanged,
  });


  Widget _buildLabel() {
    final String imgUrl = value ? 'images/icons/checkbox_filled.png' : 'images/icons/checkbox.png';
    return Image.asset(imgUrl, height: 18, width: 18,);
  }

  Widget _buildText() {
    return Flexible(child: Text(text, style: GoogleFonts.inter(textStyle: TextStyle(fontSize: 14, color: Colors.black, fontWeight: value ? FontWeight.w500 : FontWeight.w400))));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: InkWell(
        onTap: () => onChanged(!value),
        splashColor: Colors.cyan.withOpacity(0.5),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 7),
          child: Row(
            children: [
              _buildLabel(),
              const SizedBox(width: 10),
              _buildText(),
            ],
          ),
        ),
      ),
    );
  }
}