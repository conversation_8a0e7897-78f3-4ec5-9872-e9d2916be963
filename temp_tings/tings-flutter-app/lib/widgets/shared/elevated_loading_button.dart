import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ElevatedLoadingButton extends StatelessWidget {
  final bool isLoading;
  final VoidCallback onClick;
  final String label;

  const ElevatedLoadingButton(
      {super.key,
      required this.isLoading,
      required this.label,
      required this.onClick});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onClick,
      style: const ButtonStyle(
          padding: MaterialStatePropertyAll(
              EdgeInsets.symmetric(horizontal: 50, vertical: 15))),
      child: isLoading
          ? Container(
              width: 24,
              height: 24,
              padding: const EdgeInsets.all(2.0),
              child: const CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 3,
              ),
            )
          : Text(
              label,
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.w600)),
            ),
    );
  }
}
