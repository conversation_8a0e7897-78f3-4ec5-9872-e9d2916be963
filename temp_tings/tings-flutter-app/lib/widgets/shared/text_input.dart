import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'conditional.dart';

class TextInput extends StatelessWidget {
  final String? title;
  final String? placeholder;
  final TextEditingController controller;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final int? minLines;
  final int? maxLines;
  final TextStyle? titleTextStyle;
  final FocusNode? focusNode;

  const TextInput(
      {super.key,
      required this.controller,
      this.title,
      this.placeholder,
      this.validator,
      this.keyboardType,
      this.minLines,
      this.maxLines,
      this.titleTextStyle,
      this.focusNode});

  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Conditional(
          condition: title != null,
          child: Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Text(title ?? '',
                style: GoogleFonts.inter(
                    textStyle: titleTextStyle ??
                        const TextStyle(
                            fontSize: 16,
                            color: Colors.black,
                            fontWeight: FontWeight.w600))),
          )),
      TextFormField(
        focusNode: focusNode,
        cursorColor: const Color(0xFFCACACA),
        controller: controller,
        validator: validator,
        keyboardType: keyboardType,
        minLines: minLines,
        maxLines: minLines,
        style: GoogleFonts.inter(
            textStyle: const TextStyle(
                fontSize: 18,
                color: Colors.black,
                decoration: TextDecoration.none)),
        decoration: InputDecoration(
          labelText: placeholder,
          focusedBorder:
              OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    ]);
  }
}
