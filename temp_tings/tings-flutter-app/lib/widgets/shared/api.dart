import 'dart:convert';
import 'dart:typed_data';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'package:tings/globals.dart';

class Api {
  static T parse<T>(
      Uint8List responseBodyBytes, T Function(Map<String, dynamic>) mapFunc) {
    return mapFunc(jsonDecode(utf8.decode(responseBodyBytes)));
  }

  static List<T> parseList<T>(
      Uint8List responseBodyBytes, T Function(Map<String, dynamic>) mapFunc) {
    var decoded = jsonDecode(utf8.decode(responseBodyBytes));
    var parsed;
    if (decoded is List) {
      parsed = decoded.cast<Map<String, dynamic>>();
    } else {
      parsed = decoded['content'].cast<Map<String, dynamic>>();
    }
    return parsed.map<T>((json) => mapFunc(json)).toList();
  }

  static Future<http.Response> get(String path,
          {Map<String, dynamic>? queryParameters}) async =>
      http.Client()
          .get(_prepareUrl(path, queryParameters: queryParameters),
              headers: await _headers())
          .then((response) {
        if (response.body != null) {
          try {
            print(jsonDecode(response.body));
            // ignore: empty_catches
          } catch (e) {}
        }
        return response;
      });

  static Future<http.Response> post(String path,
      {Map<String, dynamic>? queryParameters, Object? body}) async {
    print(body);
    return http.Client()
        .post(_prepareUrl(path, queryParameters: queryParameters),
            headers: await _headers(), body: jsonEncode(body))
        .then((response) {
      if (response.body != 'OK') {
        print(jsonDecode(response.body));
      }
      return response;
    });
  }

  static Future<http.Response> put(String path,
          {Map<String, dynamic>? queryParameters, Object? body}) async {
    print(body);
    return http.Client()
        .put(_prepareUrl(path, queryParameters: queryParameters),
        headers: await _headers(), body: jsonEncode(body))
        .then((response) {
      if (response.body != 'OK') {
        print(jsonDecode(response.body));
      }
      return response;
    });
  }


  static Future<http.Response> patch(String path,
          {Map<String, dynamic>? queryParameters, Object? body}) async =>
      http.Client().patch(_prepareUrl(path, queryParameters: queryParameters),
          headers: await _headers(), body: jsonEncode(body));

  static Future<http.Response> delete(String path,
          {Map<String, dynamic>? queryParameters, Object? body}) async =>
      http.Client().delete(_prepareUrl(path, queryParameters: queryParameters),
          headers: await _headers(), body: jsonEncode(body));

  static Future<Map<String, String>> _headers() async {
    final user = FirebaseAuth.instance.currentUser;

    Map<String, String> headers = {'Content-Type': 'application/json'};

    if (user != null) {
      String? idToken = await user.getIdToken();
      headers['authorization'] = 'Bearer $idToken';
      print(headers['authorization']);
    }
    return headers;
  }

  static Uri _prepareUrl(String path, {Map<String, dynamic>? queryParameters}) {
    var uri = Uri(
      scheme: apiScheme,
      host: apiHost,
      port: apiPort,
      path: path,
      queryParameters: queryParameters,
    );
    print(uri.toString());
    return uri;
  }
}
