import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:humanize_duration/humanize_duration.dart';

import '../profile/data/friend_activity.dart';
import '../profile/profile_avatar.dart';

class FriendActivityLine extends StatelessWidget {
  final FriendActivity item;
  final Function(String?) onTap;

  const FriendActivityLine({super.key, required this.item, required this.onTap});

  Widget getMessage() {
    if (item.template != null && item.templateVariables != null) {
      return RichText(text: TextSpan(children: item.template!.split(" ").map((e) {
        final reg = RegExp(r'^\[[A-Z_]+\]$');
        var hasMatch = reg.hasMatch(e);
        if (hasMatch) {
          var varName = e.substring(1, e.length - 1);
          var value = item.templateVariables!.firstWhere((element) => element.key == varName);
          if (value != null) {
            return TextSpan(
              text: '${value.name} ',
              recognizer: new TapGestureRecognizer()
                  ..onTap = () => onTap(value.id),
              style: const TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold)
            );
          }
        }
        return TextSpan(text: '$e ');
      }).toList(), style: const TextStyle(fontSize: 14, color: Colors.black)));
    }

    return Text(item.message,
        style: const TextStyle(fontSize: 14, color: Colors.black));
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      // onTap: () {},
      title: getMessage(),
      subtitle: Text(
        humanizeTime(item.createdAt),
        style: const TextStyle(fontSize: 12, color: Color(0xB2000000)),
      ),
      leading: ProfileAvatar(
        displayName: item.displayName,
        avatarUrl: item.avatarUrl,
        radius: 20,
      ),
    );
  }

  String humanizeTime(String createdAt) {
    final difference = DateTime.now().difference(DateTime.parse(createdAt));
    return '${humanizeDuration(difference).split(',').first} ago';
  }
}
