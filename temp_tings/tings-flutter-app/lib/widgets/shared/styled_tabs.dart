import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class StyledTabs extends StatefulWidget {
  final List<StyledTab> tabs;
  double? width;
  int? initialActive;
  Function(int)? onTabChange;

  StyledTabs({super.key,
    this.initialActive,
    this.onTabChange,
    required this.tabs,
    this.width
  });

  @override
  State<StatefulWidget> createState() => _StyledTabsState();
}

class _StyledTabsState extends State<StyledTabs> with TickerProviderStateMixin {
  late TabController _tabController;
  int _tab = 0;

  @override
  void initState() {
    super.initState();
    if (widget.initialActive != null) {
      setState(() {
        _tab = widget.initialActive!;
      });
    }
    _tabController = TabController(initialIndex: _tab, length: widget.tabs.length, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _tab = _tabController.index;
      });
      if (widget.onTabChange != null) {
        widget.onTabChange!(_tabController.index);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(top: 16),
          padding: const EdgeInsets.all(4),
          height: 41,
          width: widget.width ?? 248,
          decoration: const BoxDecoration(
            color: Color(0xFFF8F8F8),
            borderRadius: BorderRadius.all(Radius.circular(60)),
          ),
          child: Row(
              children: widget.tabs
                  .map((tab) => _TabButton(
                        title: tab.title,
                        active: _tab == widget.tabs.indexOf(tab),
                        onClick: () {
                          setState(() {
                            _tab = widget.tabs.indexOf(tab);
                          });
                          _tabController.animateTo(widget.tabs.indexOf(tab));
                        },
                      ))
                  .toList()),
        ),
        const SizedBox(
          height: 32,
        ),
        Expanded(
          child: TabBarView(
              controller: _tabController,
              children: widget.tabs.map((tab) => tab.widget).toList()),
        )
      ],
    );
  }
}

class StyledTab {
  final String title;
  final Widget widget;

  StyledTab({required this.title, required this.widget});
}

class _TabButton extends StatelessWidget {
  final VoidCallback onClick;
  final bool active;
  final String title;

  const _TabButton(
      {super.key,
      required this.onClick,
      required this.active,
      required this.title});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onClick,
      child: Container(
        height: 33,
        width: 120,
        decoration: BoxDecoration(
          color: active ? Colors.black : Colors.transparent,
          borderRadius: const BorderRadius.all(Radius.circular(60)),
        ),
        child: Center(
            child: Text(
          title,
          style: GoogleFonts.inter(
              textStyle: TextStyle(
                  color: active ? Colors.white : Colors.black,
                  fontSize: 14,
                  fontWeight: active ? FontWeight.w600 : FontWeight.w400)),
        )),
      ),
    );
  }
}
