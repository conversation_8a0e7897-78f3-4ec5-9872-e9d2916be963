import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../profile/notifications.dart';
import 'api.dart';
import 'conditional.dart';

class NotificationsButton extends StatefulWidget {
  const NotificationsButton({super.key});

  @override
  State<StatefulWidget> createState() => _NotificationsButtonState();
}

class _NotificationsButtonState extends State<NotificationsButton> {
  bool _hasNewMessages = false;

  @override
  void initState() {
    _getHasNewNotification();
    super.initState();
  }

  void _getHasNewNotification() async {
    var prefs = await SharedPreferences.getInstance();
    var lastDate = prefs.getString('lastSeenNotificationDate');
    lastDate ??= '${DateTime(2022).toIso8601String()}Z';
    var currentDate = '${DateTime.now().toIso8601String()}Z';
    await prefs.setString('lastSeenNotificationDate', currentDate);
    final response = await Api.post('/api/notifications/has_new_messages',
        body: {'date': lastDate});
    if (response.statusCode == 200) {
      setState(() {
        _hasNewMessages = response.body == 'true';
      });
    } else {
      throw Exception('Failed to load viewed products');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topRight,
      children: [
        IconButton(
          onPressed: () {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => const Notifications()))
                .then((value) => _getHasNewNotification());
          },
          icon: Image.asset(
            "images/iconly/Light/Notification.png",
            width: 24,
            height: 24,
          ),
          color: Colors.black,
        ),
        Conditional(
          condition: _hasNewMessages,
          child: Container(
            margin: const EdgeInsets.only(top: 10, right: 12),
            width: 11,
            height: 11,
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(5)),
                border: Border.all(color: Colors.white, width: 2),
                color: const Color(0xFFFFC107)),
          ),
        ),
      ],
    );
  }
}