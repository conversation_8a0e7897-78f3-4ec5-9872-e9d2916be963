import 'package:flutter/material.dart';
import 'package:tings/widgets/friends/data/followers_count.dart';

import 'api.dart';
import 'conditional.dart';

class FollowersCounter extends StatefulWidget {
  final String? profileId;
  final Future Function() onFollowersTap;
  final Future Function() onFollowingTap;

  const FollowersCounter({
    super.key,
    this.profileId,
    required this.onFollowersTap,
    required this.onFollowingTap
  });

  @override
  State<StatefulWidget> createState() => _FollowersCounterState();
}

class _FollowersCounterState extends State<FollowersCounter> {
  FollowersCount? followersCount;

  @override
  void initState() {
    getFollowersCount();
    super.initState();
  }


  Future getFollowersCount() async {
    if (widget.profileId != null) {
      var res = await Api.get('/api/followers_count/${widget.profileId}');
      setState(() {
        followersCount = Api.parse(res.bodyBytes, FollowersCount.from<PERSON>son);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Conditional(
        condition: followersCount != null,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton(
              style: TextButton.styleFrom(
                textStyle: const TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                    decoration: TextDecoration.underline),
              ),
              onPressed: () {
                widget.onFollowersTap().then((value) {
                  getFollowersCount();
                });
              },
              child: Text('${followersCount?.followers.toString()} followers',
                  style: TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                      decoration: TextDecoration.underline)),
            ),
            const Text('|',
                style: TextStyle(
                    fontSize: 16,
                    color: Colors.black)),
            TextButton(
              style: TextButton.styleFrom(
                textStyle: const TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                    decoration: TextDecoration.underline),
              ),
              onPressed: () {
                widget.onFollowingTap().then((value) {
                  getFollowersCount();
                });
              },
              child: Text('${followersCount?.following.toString()} following',
                  style: TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                      decoration: TextDecoration.underline)),
            )
          ],
        )
    );
  }
}