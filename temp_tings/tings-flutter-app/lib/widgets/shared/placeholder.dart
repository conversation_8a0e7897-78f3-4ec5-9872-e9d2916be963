import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/shared/conditional.dart';

class TingsPlaceholder extends StatelessWidget {
  final String text;
  final String? actionText;
  final VoidCallback? onActionTap;

  const TingsPlaceholder(
      {super.key, required this.text, this.actionText, this.onActionTap});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          const SizedBox(
            height: 50,
          ),
          Image.asset(
            'images/bag_face.png',
            width: 56,
            height: 56,
          ),
          const SizedBox(
            height: 11,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(text,
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 16, color: Color(0xFF797979))),
                textAlign: TextAlign.center),
          ),
          const SizedBox(
            height: 11,
          ),
          Conditional(
            condition: actionText != null,
            child: InkWell(
              onTap: onActionTap,
              child: Text(
                '$actionText',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                        decoration: TextDecoration.underline)),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
