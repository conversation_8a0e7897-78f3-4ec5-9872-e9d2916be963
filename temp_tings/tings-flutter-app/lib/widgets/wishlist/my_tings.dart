import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/loading.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/widgets/wishlist_list.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';
import 'package:tings/widgets/wishlist/wishlist_items_list.dart';

import '../shared/event_tracker.dart';
import 'widgets/wishlist_info.dart';
import 'wishlist_create_form.dart';

class MyTings extends StatefulWidget {
  const MyTings({super.key});

  @override
  State<StatefulWidget> createState() => _MyTingsState();
}

class _MyTingsState extends State<MyTings> {
  bool _loading = true;
  List<Wishlist> _wishlists = [];

  @override
  void initState() {
    init();
    EventTracker().track('View My Tings');
    super.initState();
  }

  void init() async {
    var prefs = await SharedPreferences.getInstance();
    var wishlistInfoApproved = prefs.getBool('wishlistInfoApproved');
    getWishlists();

    if (wishlistInfoApproved != true) {
      showInfoDialog();
    }

    await prefs.setBool('wishlistInfoApproved', true);
  }

  void showInfoDialog() {
    showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          scrollable: true,
          content: WishlistInfo(onClose: (){
            Navigator.of(context).pop();
            },)
        )
    );
  }

  Future getWishlists() async {
    setState(() {
      _loading = true;
    });

    var wishlists = await WishlistDataProvider.getWishlistList();
    setState(() {
      _wishlists = wishlists;
      _loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'MyTings',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.pushNamed(
                context,
                WishlistCreateForm.routeName,
              ).then((value) async {
                if (value == true) {
                  await getWishlists();
                }
              });
            },
            icon: Image.asset(
              "images/icons/add-circle.png",
              width: 24,
              height: 24,
            ),
            color: Colors.black,
          )
        ],
      ),
      body: Column(
        mainAxisAlignment:
            _loading ? MainAxisAlignment.center : MainAxisAlignment.start,
        children: [
          Expanded(
            child: Conditional(
              condition: _wishlists.isEmpty,
              alternate: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 35),
                child: WishlistList(
                  list: _wishlists,
                  onClick: (wishlist) {
                    Navigator.pushNamed(
                      context,
                      WishlistItemsList.routeName,
                      arguments: WishlistItemsListArguments(wishlist, true),
                    ).then((value) async {
                      await getWishlists();
                    });
                  },
                ),
              ),
              child: Center(
                child: Conditional(
                  condition: _loading,
                  alternate: Text('No wishlists found',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 20, fontWeight: FontWeight.w700)),
                      textAlign: TextAlign.center),
                  child: Loading(loading: _loading),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
