import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/elevated_loading_button.dart';
import 'package:tings/widgets/wishlist/data/calendar_event.dart';

import 'widgets/wishlist_form.dart';

class CalendarEventEditFormArguments {
  final CalendarEvent event;

  CalendarEventEditFormArguments(this.event);
}

class CalendarEventEditForm extends StatefulWidget {
  final CalendarEvent event;
  const CalendarEventEditForm({super.key, required this.event});

  static const String routeName = '/edit-calendar-event';

  @override
  State<StatefulWidget> createState() => _CalendarEventEditFormState();
}

class _CalendarEventEditFormState extends State<CalendarEventEditForm> {
  CalendarEvent? _event;
  bool _isLoading = false;

  @override
  void initState() {
    setState(() {
      _event = widget.event;
    });
    super.initState();
  }

  GlobalKey<FormState>? _formKey;

  void submit() async {
    if (_formKey != null && _formKey!.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });
      var response = await Api.put('/api/calendar_event/${widget.event.id}',
          body: _event!.toJson());
      if (response.statusCode == 200) {
        Navigator.of(context).pop(_event);
      } else {
        throw Exception('Failed to save calendar event');
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Edit event',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Image.asset(
              "images/icons/clear.png",
              width: 18,
              height: 18,
            ),
            color: Colors.black,
          )
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: WishlistForm(
              isRegistry: false,
              isEvent: true,
              event: _event,
              onChange: (wishlist, formKey) {},
              onEventChange: (event, formKey) {
                setState(() {
                  if (event != null) {
                    _event = event;
                  }
                  _formKey = formKey;
                });
              },
            ),
          ),
          Padding(
            padding:
                const EdgeInsets.only(top: 18, left: 32, right: 32, bottom: 30),
            child: ElevatedLoadingButton(
                onClick: submit, isLoading: _isLoading, label: 'Save changes'),
          )
        ],
      ),
    );
  }
}
