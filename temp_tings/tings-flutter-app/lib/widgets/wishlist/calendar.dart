import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tings/widgets/friends/friend_card.dart';
import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/profile/profile_avatar.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/loading.dart';
import 'package:tings/widgets/wishlist/calendar_event_create_form.dart';
import 'package:tings/widgets/wishlist/data/calendar_event.dart';
import 'package:tings/widgets/wishlist/data/upcoming_event.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';

import '../shared/event_tracker.dart';
import 'calendar_event_edit_form.dart';
import 'calendar_info.dart';
import 'wishlist_items_list.dart';

class Calendar extends StatefulWidget {
  const Calendar({super.key});

  @override
  State<StatefulWidget> createState() => _CalendarState();
}

class _CalendarState extends State<Calendar> {
  bool _loading = true;
  final List<UpcomingEvent> _items = [];
  final List<DateTime> _months = [];
  late DateTime _selectedMonth;

  @override
  void initState() {
    init();
    EventTracker().track('View Calendar');
    super.initState();
  }

  void init() async {
    var prefs = await SharedPreferences.getInstance();
    var calendarInfoApproved = prefs.getBool('calendarInfoApproved');

    List<DateTime> months = [];
    DateTime current = DateTime.now();
    for (int i = 0; i < 12; i++) {
      int year = current.year;
      int month = current.month + i;
      if (month > 12) {
        year++;
        month -= 12;
      }
      months.add(DateTime(year, month));
    }
    setState(() {
      _selectedMonth = months[0];
      _months.addAll(months);
    });
    getCalendar();


    if (calendarInfoApproved != true) {
      showInfoDialog();
    }

    await prefs.setBool('calendarInfoApproved', true);
  }

  void showInfoDialog() {
    showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
            scrollable: true,
            content: CalendarInfo(onClose: (){
              Navigator.of(context).pop();
            },)
        )
    );
  }

  Future getCalendar() async {
    setState(() {
      _loading = true;
    });
    var items = await WishlistDataProvider.getCalendar(
        _selectedMonth.year, _selectedMonth.month);

    setState(() {
      _items.clear();
      _items.addAll(items);
      _loading = false;
    });
  }

  void remove(UpcomingEvent event) {
    var id = event.id!.split(':').last;
    Api.delete('/api/calendar_event/$id').then((response) {
      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Event deleted')));
      } else {
        throw Exception('Failed to delete calendar event');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Calendar',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.pushNamed(
                context,
                CalendarEventCreateForm.routeName,
              ).then((value) async {
                if (value == true) {
                  await getCalendar();
                }
              });
            },
            icon: Image.asset(
              "images/icons/add-circle.png",
              width: 24,
              height: 24,
            ),
            color: Colors.black,
          )
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 20,
          ),
          buildCalendar(),
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Divider(color: Color(0xFFE6E6E6), thickness: 1),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              DateFormat('MMMM yyyy').format(_selectedMonth),
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                      fontWeight: FontWeight.w700)),
              textAlign: TextAlign.left,
            ),
          ),
          const SizedBox(
            height: 24,
          ),
          Conditional(
            condition: _loading || _items.isEmpty,
            alternate: Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ListView.separated(
                  itemCount: _items.length,
                  itemBuilder: (context, index) => CalendarEventChip(
                      item: _items[index],
                      onDismissed: () {
                        remove(_items[index]);
                      },
                      onUpdate: () {
                        getCalendar();
                      }),
                  separatorBuilder: (context, index) => const SizedBox(
                    height: 18,
                  ),
                ),
              ),
            ),
            child: Expanded(
              child: Center(
                child: Conditional(
                  condition: _loading,
                  alternate: Text('No events found',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 20, fontWeight: FontWeight.w700)),
                      textAlign: TextAlign.center),
                  child: Loading(loading: _loading),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildCalendar() {
    return SizedBox(
      height: 56,
      child: ListView.separated(
        separatorBuilder: (context, index) => const SizedBox(
          width: 10,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        shrinkWrap: true,
        itemCount: _months.length,
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          return _Month(
            onTap: () {
              setState(() {
                _selectedMonth = _months[index];
              });
              getCalendar();
            },
            active: _selectedMonth.compareTo(_months[index]) == 0,
            label: DateFormat('MMM').format(_months[index]).toUpperCase(),
          );
        },
      ),
    );
  }
}

class _Month extends StatelessWidget {
  final bool active;
  final String label;
  final VoidCallback onTap;

  const _Month(
      {super.key,
      required this.active,
      required this.label,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 56,
        height: 60,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
            color: active ? Colors.black : const Color(0xFFF3F3F3),
            borderRadius: const BorderRadius.all(Radius.circular(10))),
        child: Center(
          child: Text(label,
              style: GoogleFonts.inter(
                  textStyle: TextStyle(
                fontSize: 14,
                color: active ? Colors.white : Colors.black,
              )),
              textAlign: TextAlign.left),
        ),
      ),
    );
  }
}

class CalendarEventChip extends StatelessWidget {
  final UpcomingEvent item;
  final VoidCallback? onUpdate;
  final VoidCallback? onDismissed;

  const CalendarEventChip(
      {super.key, this.onUpdate, this.onDismissed, required this.item});

  @override
  Widget build(BuildContext context) {
    var widget = InkWell(
      onTap: () async {
        if (item.id!.startsWith("birthday")) {
          var response = await Api.get('/api/profile/' + item.owner!);

          if (response.statusCode == 200) {
            var profile = Api.parse(response.bodyBytes, Profile.fromJson);
            Navigator.of(context)
                .push(MaterialPageRoute(
                builder: (context) => FriendCard(profile: profile)));
          }
        } else if (item.id!.startsWith("calendar_event:PRIVATE")) {
          Navigator.pushNamed(context, CalendarEventEditForm.routeName,
              arguments: CalendarEventEditFormArguments(CalendarEvent(
                  name: item.name,
                  eventDate: item.registryDate,
                  iconUrl: item.avatarUrl,
                  id: item.id!.split(":").last)));
        } else if (item.id!.startsWith("registry")) {
          var id = item.id!.replaceFirst('registry-', '');
          var wishlist = await WishlistDataProvider.getWishlistById(id);
          if (wishlist != null) {
            Navigator.pushNamed(
              context,
              WishlistItemsList.routeName,
              arguments: WishlistItemsListArguments(wishlist, false),
            );
          }
        }
      },
      child: Container(
        height: 66,
        clipBehavior: Clip.hardEdge,
        padding: const EdgeInsets.only(left: 5, top: 5, bottom: 5, right: 30),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(90)),
        ),
        child: Row(
          children: [
            ProfileAvatar(
              avatarUrl: item.avatarUrl,
              displayName: item.name,
              radius: 28,
              color: Colors.white,
              labelColor: Colors.white,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(item.name,
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.w600)),
                      textAlign: TextAlign.left),
                  const SizedBox(
                    height: 2,
                  ),
                  Text(
                      (item.ownerName != null
                          ? ('${item.ownerName!}, ')
                          : '') +
                          DateFormat('MMM d')
                              .format(DateTime.parse(item.registryDate!)),
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(fontSize: 12)),
                      textAlign: TextAlign.left)
                ],
              ),
            )
          ],
        ),
      ),
    );

    if (item.id!.startsWith("calendar_event:PRIVATE")) {
      return wrapInDismissible(widget, context);
    }
    return widget;
  }

  Widget wrapInDismissible(Widget child, BuildContext context) {
    return Dismissible(
      key: Key(item.id!),
      confirmDismiss: (direction) {
        return showDialog(
            context: context,
            builder: (BuildContext context) => AlertDialog(
              title: Text(
                'Delete event',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 16, fontWeight: FontWeight.w700)),
                textAlign: TextAlign.left,
              ),
              content: Text(
                'Are you sure you want to delete this event?',
                style: GoogleFonts.inter(
                    textStyle:
                    const TextStyle(color: Colors.black, fontSize: 16)),
                textAlign: TextAlign.left,
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: const Text('Cancel',
                      style: TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                          decoration: TextDecoration.underline)),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                  },
                  child: const Text('Delete',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.red,
                      )),
                ),
              ],
            ));
      },
      background: Container(
        alignment: Alignment.centerLeft,
        color: const Color(0xFFB6332A),
        child: const Padding(
          padding: EdgeInsets.all(16.0),
          child: Icon(
            Icons.delete_outline,
            color: Colors.white,
            size: 30,
          ),
        ),
      ),
      secondaryBackground: Container(
        alignment: Alignment.centerRight,
        color: const Color(0xFFB6332A),
        child: const Padding(
          padding: EdgeInsets.all(16.0),
          child: Icon(
            Icons.delete_outline,
            color: Colors.white,
            size: 30,
          ),
        ),
      ),
      onDismissed: (direction) {
        if (onDismissed != null) {
          onDismissed!();
        }
      },
      child: child,
    );
  }
}
