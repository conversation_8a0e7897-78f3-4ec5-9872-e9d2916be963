import 'package:tings/widgets/product/data/product.dart';

class WishlistItem {
  String? id;
  int quantity;
  String? note;
  int purchased;
  bool? anonymous;
  Product product;
  String wishListId;

  WishlistItem(
      {this.id,
      required this.purchased,
      required this.quantity,
      required this.product,
      required this.wishListId,
      this.note,
      this.anonymous});

  factory WishlistItem.fromJson(Map<String, dynamic> json, Product? product) {
    return WishlistItem(
      id: json['id'],
      wishListId: json['wishListId'],
      quantity: json['quantity'],
      product: product ?? Product.fromJson(json['product']),
      note: json['note'] ?? '',
      purchased: json['purchased'],
      anonymous: json['anonymous'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id.toString(),
      'quantity': quantity,
      'note': note,
      'anonymous': anonymous,
      'purchased': purchased,
      'wishListId': wishListId,
      'productId': product.id,
    };
  }
}
