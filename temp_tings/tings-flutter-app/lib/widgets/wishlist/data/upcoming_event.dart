/**
 *
    val id: String,
    val name: String,
    val registryDate: LocalDate,
    val owner: UUID,
    val avatarUrl: String? = null,
 */
class UpcomingEvent {
  final String? id;
  String name;
  String? registryDate;
  String? avatarUrl;
  String? owner;
  String? ownerName;

  UpcomingEvent(
      {this.id,
      required this.name,
      this.registryDate,
      this.avatarUrl,
      this.owner,
      this.ownerName});

  factory UpcomingEvent.fromJson(Map<String, dynamic> json) {
    return UpcomingEvent(
      id: json['id'],
      avatarUrl: json['avatarUrl'],
      registryDate: json['registryDate'],
      owner: json['owner'],
      ownerName: json['ownerName'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'avatarUrl': avatarUrl,
      'name': name,
      'registryDate': registryDate,
      'owner': owner,
    };
  }
}
