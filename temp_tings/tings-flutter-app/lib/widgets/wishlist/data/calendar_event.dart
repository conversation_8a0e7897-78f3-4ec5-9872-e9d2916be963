
class CalendarEvent {
  final String? id;
  String name;
  String? eventDate;
  String? iconUrl;

  CalendarEvent(
      {
      this.id,
      required this.name,
      required this.eventDate,
      required this.iconUrl
      });

  factory CalendarEvent.fromJson(Map<String, dynamic> json) {
    return CalendarEvent(
      id: json['id'],
      iconUrl: json['iconUrl'],
      eventDate: json['eventDate'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'iconUrl': iconUrl,
      'name': name,
      'eventDate': eventDate,
    };
  }
}