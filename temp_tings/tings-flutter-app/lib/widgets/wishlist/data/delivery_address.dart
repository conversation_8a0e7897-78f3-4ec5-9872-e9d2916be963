class DeliveryAddress {
  final String? street;
  final String? unit;
  final String? city;
  final String? state;
  final String? zip;

  DeliveryAddress({this.street, this.unit, this.city, this.state, this.zip});

  factory DeliveryAddress.fromJson(Map<String, dynamic> json) {
    return DeliveryAddress(
      street: json['street'],
      unit: json['unit'],
      city: json['city'],
      state: json['state'],
      zip: json['zip'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'unit': unit,
      'city': city,
      'state': state,
      'zip': zip,
    };
  }
}
