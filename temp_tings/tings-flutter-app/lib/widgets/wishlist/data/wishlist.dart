import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/wishlist/data/delivery_address.dart';

class Wishlist {
  final String? id;
  String? iconUrl;
  String? registryDate;
  String name;
  WishlistType type;
  bool private;
  int? itemCount;
  DeliveryAddress? deliveryAddress;
  final List<Profile> collaborators;

  Wishlist(
      {this.iconUrl,
      required this.name,
      required this.private,
      required this.type,
      this.registryDate,
      this.itemCount,
      this.deliveryAddress,
      this.id,
      required this.collaborators});

  factory Wishlist.fromJson(Map<String, dynamic> json) {
    return Wishlist(
      id: json['id'],
      iconUrl: json['iconUrl'],
      type: json['type'] == 'REGISTRY'
          ? WishlistType.registry
          : WishlistType.wishlist,
      registryDate: json['registryDate'],
      itemCount: json['itemCount'],
      name: json['name'],
      private: json['private'],
      deliveryAddress: json['deliveryAddress'] != null
          ? DeliveryAddress.fromJson(json['deliveryAddress'])
          : null,
      collaborators: json['collaborators'] != null
          ? List<Profile>.from((json['collaborators'] as List<dynamic>)
              .map((e) => Profile.fromJson(e)))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'iconUrl': iconUrl,
      'name': name,
      'registryDate': registryDate,
      'private': private,
      'deliveryAddress':
          deliveryAddress != null ? deliveryAddress!.toJson() : null,
      'type': type == WishlistType.registry ? 'REGISTRY' : 'WISH_LIST',
      'collaborators': collaborators.map((e) => e.id).toList()
    };
  }
}

enum WishlistType { wishlist, registry }
