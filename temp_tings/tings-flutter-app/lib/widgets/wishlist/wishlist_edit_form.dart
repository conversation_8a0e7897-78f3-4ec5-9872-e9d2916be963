import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/shared/elevated_loading_button.dart';
import 'package:tings/widgets/shared/radio_option.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';

import 'widgets/wishlist_form.dart';

class WishlistEditFormArguments {
  final Wishlist wishlist;

  WishlistEditFormArguments(this.wishlist);
}

class WishlistEditForm extends StatefulWidget {
  final Wishlist wishlist;
  const WishlistEditForm({super.key, required this.wishlist});
  static const String routeName = '/edit-wishlist';

  @override
  State<StatefulWidget> createState() => _WishlistEditFormState();
}

class _WishlistEditFormState extends State<WishlistEditForm> {
  late Wishlist _wishlist;
  bool _isLoading = false;

  GlobalKey<FormState>? _formKey;

  @override
  void initState() {
    super.initState();

    setState(() {
      _wishlist = widget.wishlist;
    });
  }

  void submit() async {
    if (_formKey != null) {
      if (_formKey!.currentState!.validate()) {
        setState(() {
          _isLoading = true;
        });
        var response = await WishlistDataProvider.updateWishlist(_wishlist);
        if (response != null) {
          success();
        } else {
          throw Exception('Failed to save wishlist');
        }
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void success() {
    Navigator.of(context).pop(_wishlist);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Edit list',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Image.asset(
              "images/icons/clear.png",
              width: 18,
              height: 18,
            ),
            color: Colors.black,
          )
        ],
      ),
      body: Column(
        children: [
          const SizedBox(
            height: 10,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              children: [
                Text(
                  'List type',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 12,
                    color: Colors.black,
                  )),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 32),
                  child: RadioOption(
                    groupValue: _wishlist.type,
                    text: 'Wishlist',
                    value: WishlistType.wishlist,
                    onChanged: (type) {
                      setState(() {
                        _wishlist.type = WishlistType.wishlist;
                      });
                    },
                  ),
                ),
                RadioOption(
                  groupValue: _wishlist.type,
                  text: 'Registry',
                  value: WishlistType.registry,
                  onChanged: (type) {
                    setState(() {
                      _wishlist.type = WishlistType.registry;
                    });
                  },
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 32,
          ),
          Expanded(
            child: WishlistForm(
              isRegistry: _wishlist.type == WishlistType.registry,
              isEvent: false,
              wishlist: _wishlist,
              onChange: (wishlist, formKey) {
                setState(() {
                  if (wishlist != null) {
                    _wishlist = wishlist;
                  }
                  _formKey = formKey;
                });
              },
            ),
          ),
          Padding(
            padding:
                const EdgeInsets.only(top: 18, left: 32, right: 32, bottom: 30),
            child: ElevatedLoadingButton(
                onClick: submit, isLoading: _isLoading, label: 'Save changes'),
          )
        ],
      ),
    );
  }
}
