import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/loading.dart';
import 'package:tings/widgets/wishlist/data/wishlist_item.dart';
import 'package:transparent_image/transparent_image.dart';

import '../shared/event_tracker.dart';

class PurchasedItemsArguments {
  final List<WishlistItem> items;
  final int total;

  PurchasedItemsArguments({required this.items, required this.total});
}

class PurchasedItems extends StatefulWidget {
  final List<WishlistItem> items;
  final int total;
  static const String routeName = '/purchased-items';

  const PurchasedItems({super.key, required this.items, required this.total});

  @override
  State<StatefulWidget> createState() => _PurchasedItemsState();
}

class _PurchasedItemsState extends State<PurchasedItems> {
  bool _loading = true;
  List<_FullPurchase> _items = List.empty(growable: true);

  @override
  void initState() {
    EventTracker().track('View Purchased Items');
    super.initState();
    load();
  }

  Future load() async {
    setState(() {
      _loading = true;
    });
    var ids = widget.items.map((e) => e.id);
    var response = await Api.get('/api/purchase/by_wish_list_item_full',
        queryParameters: {'ids': ids});
    var list = Api.parseList(response.bodyBytes, _FullPurchase.fromJson);
    setState(() {
      _items.addAll(list);
      _loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            'Purchased items',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.w700)),
            textAlign: TextAlign.center,
          ),
          bottom: PreferredSize(
              preferredSize: const Size.fromHeight(32),
              child: Column(
                children: [
                  Text(
                      '${widget.items.isNotEmpty ? widget.items.map((i) => i.purchased).reduce((a, b) => a + b) : 0} of ${widget.total}',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                        fontSize: 12,
                        color: Colors.black,
                      )),
                      textAlign: TextAlign.left),
                  const SizedBox(
                    height: 16,
                  )
                ],
              )),
          centerTitle: true,
          actions: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Image.asset(
                "images/icons/clear.png",
                width: 18,
                height: 18,
              ),
              color: Colors.black,
            )
          ],
        ),
        body: Column(
          children: [
            Expanded(
                child: _loading
                    ? const Loading(loading: true)
                    : ListView.separated(
                        itemCount: _items.length,
                        itemBuilder: (context, index) =>
                            buildLine(_items[index]),
                        separatorBuilder: (context, index) => const SizedBox(
                          height: 18,
                        ),
                      )),
          ],
        ));
  }

  Widget buildLine(_FullPurchase item) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: InkWell(
        onTap: () {},
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 4),
              clipBehavior: Clip.hardEdge,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(4)),
              ),
              child: item.wishListItem.product.thumbnail?.url != null
                  ? FadeInImage.memoryNetwork(
                      placeholder: kTransparentImage,
                      image: item.wishListItem.product.thumbnail!.url,
                      fit: BoxFit.cover,
                      width: 46,
                      height: 46,
                    )
                  : Image.asset(
                      'images/tings_place.png',
                      fit: BoxFit.cover,
                      width: 46,
                      height: 46,
                    ) as Widget,
            ),
            const SizedBox(
              width: 16,
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(item.wishListItem.product.title,
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 18,
                              color: Colors.black,
                              fontWeight: FontWeight.w600)),
                      textAlign: TextAlign.left),
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Image.asset(
                          'images/iconly/Light/Calendar_small.png',
                          height: 12,
                          width: 12,
                        ),
                        const SizedBox(
                          width: 4,
                        ),
                        Text(
                          'Purchased on ${DateFormat('MMM d, yyyy').format(DateFormat("yyyy-MM-ddTHH:mm:ssZ").parseUTC(item.createdAt).toLocal())}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.black,
                          ),
                        )
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Row(
                      children: [
                        Image.asset(
                          'images/iconly/Light/Profile.png',
                          width: 12,
                          height: 12,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: Text(
                              item.anonymous
                                  ? 'Anonymous'
                                  : item.buyer.displayName,
                              style: GoogleFonts.inter(
                                  textStyle: const TextStyle(
                                fontSize: 12,
                                color: Colors.black,
                              )),
                              textAlign: TextAlign.left),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FullPurchase {
  WishlistItem wishListItem;
  String createdAt;
  bool anonymous;
  Profile buyer;

  _FullPurchase(
      {required this.wishListItem,
      required this.anonymous,
      required this.buyer,
      required this.createdAt});

  factory _FullPurchase.fromJson(Map<String, dynamic> json) {
    return _FullPurchase(
      wishListItem: WishlistItem.fromJson(json['wishListItem'], null),
      anonymous: json['anonymous'],
      buyer: Profile.fromJson(json['buyer']),
      createdAt: json['createdAt'],
    );
  }
}
