import 'dart:convert';
import 'dart:typed_data';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:tings/widgets/product/data/product.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/data/wishlist_item.dart';

import 'data/upcoming_event.dart';

class WishlistDataProvider {
  static IDataProvider _provider = _WebDataProvider();

  static Future init() async => _getProvider().init();

  static Future clear() async => _getProvider().clear();

  static Future updateWishlist(Wishlist wishlist) async =>
      _getProvider().updateWishlist(wishlist);

  static Future<Wishlist?> addWishlist(Wishlist wishlist) async =>
      _getProvider().addWishlist(wishlist);

  static Future<List<UpcomingEvent>> getCalendar(int year, int month) async =>
      _getProvider().getCalendar(year, month);

  static Future<List<UpcomingEvent>> getUpcomingEvents() async =>
      _getProvider().getUpcomingEvents();

  static Future<List<Wishlist>> getWishlistList(
          {String? clientProfileId}) async =>
      _getProvider().getWishlistList(clientProfileId: clientProfileId);

  static Future<Wishlist?> getWishlistById(String id) async =>
      _getProvider().getWishlistById(id);

  static Future removeWishlist(String wishlistId) async =>
      _getProvider().removeWishlist(wishlistId);

  static Future<List<WishlistItem>> getWishlistItems(String wishlistId) async =>
      _getProvider().getWishlistItems(wishlistId);

  static Future<List<WishlistItem>> getWishlistItemsByProduct(
          Product product) async =>
      _getProvider().getWishlistItemsByProduct(product);

  static Future updateItem(WishlistItem wishlistItem) async =>
      _getProvider().updateItem(wishlistItem);

  static Future<WishlistItem?> addItem(Product product, String wishlistId,
          {int? quantity, int? purchased, String? note}) async =>
      _getProvider().addItem(product, wishlistId,
          quantity: quantity, purchased: purchased, note: note);

  static Future<bool> getPurchaseStatus(String wishlistItemId) async =>
      _getProvider().getPurchaseStatus(wishlistItemId);

  static Future removeItem(String wishlistId) async =>
      _getProvider().removeItem(wishlistId);

  static IDataProvider _getProvider() {
    final user = FirebaseAuth.instance.currentUser;
    return WishlistDataProvider._provider;
    // return user != null ? _WebDataProvider() : _LocalDataProvider();
  }
}

abstract class IDataProvider {
  Future init();
  Future clear();
  Future<List<UpcomingEvent>> getCalendar(int year, int month);
  Future<List<UpcomingEvent>> getUpcomingEvents();
  Future<bool> getPurchaseStatus(String wishlistItemId);
  Future<List<Wishlist>> getWishlistList({String? clientProfileId});
  Future<Wishlist?> getWishlistById(String id);
  Future<List<WishlistItem>> getWishlistItems(String wishlistId);
  Future<List<WishlistItem>> getWishlistItemsByProduct(Product product);
  Future removeWishlist(String wishlistId);
  Future<Wishlist?> addWishlist(Wishlist wishlist);
  Future updateWishlist(Wishlist wishlist);
  Future<WishlistItem?> addItem(Product product, String wishlistId,
      {int? quantity, int? purchased, String? note});
  Future updateItem(WishlistItem wishlistItem);
  Future removeItem(String wishlistId);
}

class _WebDataProvider implements IDataProvider {
  List<Wishlist> _wishlists = [];

  @override
  Future<WishlistItem?> addItem(Product product, String wishlistId,
      {int? quantity, int? purchased, String? note}) async {
    var response = await Api.post('/api/wish_list_item', body: {
      'productId': product.id,
      'wishListId': wishlistId,
      'note': note ?? '',
      'purchased': purchased,
      'quantity': quantity
    });
    if (response.statusCode == 200) {
      await _refresh();
      var json = jsonDecode(response.body);
      return WishlistItem.fromJson(json, product);
    } else {
      return null;
    }
  }

  @override
  Future<Wishlist?> addWishlist(Wishlist wishlist) async {
    var response = await Api.post('/api/wish_list', body: wishlist.toJson());
    if (response.statusCode == 200) {
      await _refresh();
      return Api.parse(response.bodyBytes, Wishlist.fromJson);
    } else {
      return null;
    }
  }

  @override
  Future<bool> getPurchaseStatus(String wishlistItemId) async {
    var response =
        await Api.get('/api/purchase/by_wish_list_item/$wishlistItemId');
    if (response.statusCode == 200) {
      return Api.parse(response.bodyBytes, (res) => res['hasPurchase']);
    } else {
      return false;
    }
  }

  @override
  Future<List<UpcomingEvent>> getCalendar(int year, int month) async {
    var response = await Api.get('/api/calendar/$year/$month');
    if (response.statusCode == 200) {
      return Api.parseList(response.bodyBytes, UpcomingEvent.fromJson);
    } else {
      return [];
    }
  }

  @override
  Future<List<UpcomingEvent>> getUpcomingEvents() async {
    var response = await Api.get('/api/upcoming_events');
    if (response.statusCode == 200) {
      return Api.parseList(response.bodyBytes, UpcomingEvent.fromJson);
    } else {
      return [];
    }
  }

  @override
  Future<List<Wishlist>> getWishlistList({String? clientProfileId}) async {
    if (clientProfileId != null) {
      var response =
          await Api.get('/api/wish_list/by_profile/$clientProfileId');
      if (response.statusCode == 200) {
        return Api.parseList(response.bodyBytes, Wishlist.fromJson);
      } else {
        return [];
      }
    } else {
      return _wishlists;
    }
  }

  @override
  Future<Wishlist?> getWishlistById(String id) async {
    var response = await Api.get('/api/wish_list/$id');
    if (response.statusCode == 200) {
      return Api.parse(response.bodyBytes, Wishlist.fromJson);
    } else {
      return null;
    }
  }

  @override
  Future init() async {
    await _refresh();
  }

  Future _refresh() async {
    var response = await Api.get('/api/wish_list');
    if (response.statusCode == 200) {
      _wishlists.clear();
      _wishlists.addAll(Api.parseList(response.bodyBytes, Wishlist.fromJson));
    } else {
      _wishlists = [];
    }
  }

  @override
  Future removeItem(String itemId) async {
    var response = await Api.delete('/api/wish_list_item/$itemId');
    if (response.statusCode != 200) {
      throw Exception('Failed to delete wishlist item');
    }
    await _refresh();
  }

  @override
  Future removeWishlist(String wishlistId) async {
    var response = await Api.delete('/api/wish_list/$wishlistId');
    if (response.statusCode != 200) {
      throw Exception('Failed to delete wishlist');
    }
    await _refresh();
  }

  @override
  Future updateItem(WishlistItem wishlistItem) async {
    var response = await Api.put('/api/wish_list_item/${wishlistItem.id}',
        body: wishlistItem.toJson());
    if (response.statusCode != 200) {
      throw Exception('Failed to update wishlist item');
    }
    await _refresh();
  }

  @override
  Future<Wishlist?> updateWishlist(Wishlist wishlist) async {
    print(wishlist.toJson());
    var response =
        await Api.put('/api/wish_list/${wishlist.id}', body: wishlist.toJson());
    if (response.statusCode == 200) {
      await _refresh();
      var json = jsonDecode(response.body);
      return Wishlist.fromJson(json);
    } else {
      return null;
    }
  }

  List<WishlistItem> parseWishlistItems(
      Uint8List responseBodyBytes, Product? product) {
    final parsed =
        jsonDecode(utf8.decode(responseBodyBytes)).cast<Map<String, dynamic>>();
    return parsed
        .map<WishlistItem>((json) => WishlistItem.fromJson(json, product))
        .toList();
  }

  @override
  Future<List<WishlistItem>> getWishlistItems(String wishlistId) async {
    var response =
        await Api.get('/api/wish_list_item/by_wish_list/$wishlistId');
    if (response.statusCode == 200) {
      return parseWishlistItems(response.bodyBytes, null);
    } else {
      return [];
    }
  }

  @override
  Future<List<WishlistItem>> getWishlistItemsByProduct(Product product) async {
    var response =
        await Api.get('/api/wish_list_item/by_product/${product.id}');
    if (response.statusCode == 200) {
      return parseWishlistItems(response.bodyBytes, product);
    } else {
      return [];
    }
  }

  @override
  Future clear() async {
    _wishlists = [];
  }
}
