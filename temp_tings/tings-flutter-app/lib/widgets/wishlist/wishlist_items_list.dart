import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/home.dart';
import 'package:tings/widgets/product/data/product.dart';
import 'package:tings/widgets/product/parse_product_url_form.dart';
import 'package:tings/widgets/product/products_masonry.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/loading.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/data/wishlist_item.dart';
import 'package:tings/widgets/wishlist/purchased_items.dart';
import 'package:tings/widgets/wishlist/widgets/collaborators.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';
import 'package:tings/widgets/wishlist/wishlist_edit_form.dart';

import '../shared/event_tracker.dart';

class WishlistItemsListArguments {
  final Wishlist wishlist;
  final bool isOwner;

  WishlistItemsListArguments(this.wishlist, this.isOwner);
}

class WishlistItemsList extends StatefulWidget {
  final Wishlist wishlist;
  final bool isOwner;
  static const String routeName = '/wishlist-items';

  const WishlistItemsList(
      {super.key, required this.wishlist, required this.isOwner});

  @override
  State<StatefulWidget> createState() => _WishlistItemsListState();
}

class _WishlistItemsListState extends State<WishlistItemsList> {
  final ScrollController _controller = ScrollController();
  bool _loading = true;
  late Wishlist _wishlist;
  List<WishlistItem> _wishlistItems = <WishlistItem>[];
  List<Product> _products = <Product>[];

  @override
  void initState() {
    EventTracker().track(widget.isOwner ? 'View User Wishlist' : 'View Other Wishlists');
    setState(() {
      _wishlist = widget.wishlist;
    });
    load();
    super.initState();
  }

  Future load() async {
    setState(() {
      _loading = true;
    });
    var items = await WishlistDataProvider.getWishlistItems(_wishlist.id!);
    setState(() {
      _wishlistItems.clear();
      _wishlistItems.addAll(items);
      _products = items.map((e) {
        var product = e.product;
        if (e.purchased > 0) {
          product.label = e.purchased == e.quantity
              ? 'Purchased'
              : '${e.purchased} of ${e.quantity} purchased';
        }
        return product;
      }).toList();
      _loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
        bottom: subtitle(context),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              _wishlist.name,
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 20,
                      color: Colors.black,
                      fontWeight: FontWeight.w700)),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        centerTitle: true,
        actions: [
          Conditional(
            condition: widget.isOwner,
            child: Row(
              children: [
                IconButton(
                    visualDensity: VisualDensity.compact,
                    onPressed: () {
                      Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) => ParseProductUrlForm(wishlist: widget.wishlist,)));
                    },
                    icon: Image.asset(
                      "images/icons/add-circle.png",
                      width: 24,
                      height: 24,
                    )),
                IconButton(
                  onPressed: () {
                    showModalBottomSheet<void>(
                      context: context,
                      isScrollControlled: true,
                      barrierColor: Colors.black87,
                      builder: buildBottomSheet,
                    );
                  },
                  icon: Image.asset(
                    "images/iconly/Light/More_Circle.png",
                    width: 24,
                    height: 24,
                  ),
                  color: Colors.black,
                ),
              ],
            ),
          )
        ],
      ),
      body: Conditional(
        condition: _products.isEmpty,
        alternate: Stack(
          children: <Widget>[
            ProductsMasonry(
              products: _products,
              wishlistItems: _wishlistItems,
              controller: _controller,
              isOwner: widget.isOwner,
              onClose: load,
              onReturnFromDetails: (value) {
                if (value) {
                  setState(() {
                    _loading = true;
                  });
                  load();
                }
              },
            ),
            Loading(loading: _loading),
          ],
        ),
        child: emptyListPlaceholder(context),
      ),
    );
  }

  PreferredSizeWidget subtitle(BuildContext context) => PreferredSize(
        preferredSize: Size.fromHeight(
            _wishlistItems.any((item) => item.purchased > 0) ? 168 : 116),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Conditional(
                  condition: _wishlist.private,
                  child: Row(
                    children: [
                      Image.asset(
                        'images/icons/Lock_small.png',
                        width: 12,
                        height: 12,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: Text('Private',
                            style: GoogleFonts.inter(
                                textStyle: const TextStyle(
                              fontSize: 12,
                              color: Colors.black,
                            )),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 4),
                        child: Text('•',
                            style: GoogleFonts.inter(
                                textStyle: const TextStyle(
                              fontSize: 12,
                              color: Colors.black,
                            )),
                            textAlign: TextAlign.left),
                      )
                    ],
                  ),
                ),
                Text(
                    '${_wishlistItems.isNotEmpty ? _wishlistItems.map((i) => i.quantity).reduce((a, b) => a + b) : 0} items',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                      fontSize: 12,
                      color: Colors.black,
                    )),
                    textAlign: TextAlign.left)
              ],
            ),
            avatar(),
            purchasedTile(context),
            const SizedBox(
              height: 16,
            )
          ],
        ),
      );

  Widget avatar() => Container(
        margin: const EdgeInsets.only(top: 16),
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(
              Radius.circular(_wishlist.iconUrl != null ? 40 : 4)),
        ),
        width: 72,
        height: 72,
        child: _wishlist.iconUrl != null
            ? CircleAvatar(
                minRadius: 72,
                backgroundImage: NetworkImage(_wishlist.iconUrl ?? ''))
            : Image.asset(
                'images/tings_place.png',
                fit: BoxFit.cover,
                width: 80,
                height: 80,
              ),
      );

  Widget purchasedTile(BuildContext context) => Conditional(
        condition: _wishlistItems.any((item) => item.purchased > 0),
        child: InkWell(
          onTap: () {
            Navigator.of(context).pushNamed(PurchasedItems.routeName,
                arguments: PurchasedItemsArguments(
                    items: _wishlistItems
                        .where((item) => item.purchased > 0)
                        .toList(),
                    total: _wishlistItems
                        .map((i) => i.quantity)
                        .reduce((a, b) => a + b)));
          },
          child: Container(
            margin: const EdgeInsets.only(top: 16),
            clipBehavior: Clip.hardEdge,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: const BoxDecoration(
              color: Color(0xFFFFC107),
              borderRadius: BorderRadius.all(Radius.circular(4)),
            ),
            height: 36,
            width: 206,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                    '${_wishlistItems.isNotEmpty ? _wishlistItems.map((i) => i.purchased).reduce((a, b) => a + b) : 0} items purchased',
                    style: const TextStyle(
                        color: Colors.black,
                        fontSize: 14,
                        fontWeight: FontWeight.w600),
                    textAlign: TextAlign.left),
                const Text('View',
                    style: TextStyle(
                        color: Colors.black,
                        fontSize: 14,
                        decoration: TextDecoration.underline,
                        fontWeight: FontWeight.w400),
                    textAlign: TextAlign.right),
              ],
            ),
          ),
        ),
      );

  Widget emptyListPlaceholder(BuildContext context) => Center(
        child: Conditional(
          condition: _loading,
          alternate: Column(
            children: [
              const SizedBox(
                height: 100,
              ),
              Image.asset("images/btn_mytings.png"),
              const SizedBox(
                height: 27,
              ),
              Text('No Tings added yet',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(fontSize: 16)),
                  textAlign: TextAlign.center),
              const SizedBox(
                height: 20,
              ),
              Conditional(
                condition: widget.isOwner,
                child: Container(
                  alignment: Alignment.center,
                  child: SizedBox(
                    width: 129,
                    height: 31,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pushAndRemoveUntil(
                            MaterialPageRoute(
                                builder: (context) => const HomePage()),
                            (r) => false);
                      },
                      style: const ButtonStyle(
                          padding: MaterialStatePropertyAll(
                              EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8))),
                      child: Text(
                        'Start exploring',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                                fontWeight: FontWeight.w600)),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          child: Loading(loading: _loading),
        ),
      );

  Widget buildBottomSheet(BuildContext context) {
    return BottomSheetContainer(
      title: '',
      onClose: (val) {
        Navigator.of(context).pop();
      },
      height: 251, // 291,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        editMenuItem(context),
        ownProductMenuItem(context),
        // menuItem('Duplicate', () {}),
        // collaboratorsMenuItem(context),
        // menuItem('Share', () {}),
        deleteMenuItem(context),
      ],
    );
  }

  void showCollaborators() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      barrierColor: Colors.black87,
      builder: (context) => Collaborators(
        onClose: (list) async {
          await WishlistDataProvider.updateWishlist(_wishlist);
        },
        collaborators: _wishlist.collaborators,
      ),
    );
  }

  Widget collaboratorsMenuItem(BuildContext context) =>
      menuItem('Manage collaborators', () {
        Navigator.of(context).pop();
        showCollaborators();
      });

  Widget editMenuItem(BuildContext context) => menuItem('Edit list', () {
        Navigator.of(context).pop();
        Navigator.of(context)
            .pushNamed(WishlistEditForm.routeName,
                arguments: WishlistEditFormArguments(_wishlist))
            .then((value) {
          if (value != null) {
            var count = _wishlist.itemCount;
            setState(() {
              _wishlist = value as Wishlist;
              _wishlist.itemCount = count;
            });
          }
        });
      });

  Widget ownProductMenuItem(BuildContext context) =>
      menuItem('Add my own product', () {
        Navigator.of(context).pop();
        Navigator.of(context)
            .push(MaterialPageRoute(
                builder: (context) => const ParseProductUrlForm()))
            .then((value) {
          if (value != null) {
            var count = _wishlist.itemCount;
            setState(() {
              _wishlist = value as Wishlist;
              _wishlist.itemCount = count;
            });
          }
        });
      });

  Widget deleteMenuItem(BuildContext context) => menuItem('Delete', () {
        String wishlistType =
            _wishlist.type == WishlistType.registry ? 'registry' : 'wishlist';
        Navigator.of(context).pop();
        showDialog(
            context: context,
            builder: (BuildContext context) => AlertDialog(
                  title: Text(
                    'Are you sure you want to delete this $wishlistType?',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w700)),
                    textAlign: TextAlign.left,
                  ),
                  content: Text(
                    'If you delete this $wishlistType it will also delete your saved items in this list.',
                    style: GoogleFonts.inter(
                        textStyle:
                            const TextStyle(color: Colors.black, fontSize: 16)),
                    textAlign: TextAlign.left,
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text('Cancel',
                          style: TextStyle(
                              fontSize: 16,
                              color: Colors.black,
                              decoration: TextDecoration.underline)),
                    ),
                    TextButton(
                      onPressed: () {
                        WishlistDataProvider.removeWishlist(_wishlist.id!)
                            .then((response) {
                          Navigator.of(context).pop();
                          Navigator.of(context).pop();
                          ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('$wishlistType deleted')));
                        });
                      },
                      child: const Text('Delete',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.red,
                          )),
                    ),
                  ],
                ));
      });

  Widget menuItem(String title, Function() onTap) {
    return ListTile(
      onTap: onTap,
      title: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        child: Text(title,
            style: const TextStyle(
                color: Colors.black, fontSize: 18, fontWeight: FontWeight.w400),
            textAlign: TextAlign.left),
      ),
    );
  }
}
