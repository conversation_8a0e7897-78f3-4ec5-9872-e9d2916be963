import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/elevated_loading_button.dart';
import 'package:tings/widgets/shared/styled_tabs.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';

import '../shared/event_tracker.dart';
import 'widgets/wishlist_form.dart';

class WishlistCreateForm extends StatefulWidget {
  const WishlistCreateForm({super.key});
  static const String routeName = '/create-wishlist';

  @override
  State<StatefulWidget> createState() => _WishlistCreateFormState();
}

class _WishlistCreateFormState extends State<WishlistCreateForm> {
  Wishlist? _wishlist;
  bool _isLoading = false;

  GlobalKey<FormState>? _formKey;

  @override
  void initState() {
    EventTracker().track('View New List');
    super.initState();
  }

  void submit() async {
    if (_formKey != null) {
      if (_formKey!.currentState!.validate() && _wishlist != null) {
        setState(() {
          _isLoading = true;
        });
        var response = await WishlistDataProvider.addWishlist(_wishlist!);
        if (response != null) {
          showModalBottomSheet<void>(
            context: context,
            isScrollControlled: true,
            barrierColor: Colors.black87,
            builder: buildCreatedBottomSheet,
          ).then((value) => Navigator.of(context).pop(true));
        } else {
          throw Exception('Failed to save wishlist');
        }
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'New list',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Image.asset(
              "images/icons/clear.png",
              width: 18,
              height: 18,
            ),
            color: Colors.black,
          )
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: StyledTabs(
              tabs: [
                StyledTab(
                    title: 'Wishlist',
                    widget: WishlistForm(
                      isRegistry: false,
                      isEvent: false,
                      onChange: (wishlist, formKey) {
                        setState(() {
                          _wishlist = wishlist;
                          _formKey = formKey;
                        });
                      },
                    )),
                StyledTab(
                    title: 'Registry',
                    widget: WishlistForm(
                      isRegistry: true,
                      isEvent: false,
                      onChange: (wishlist, formKey) {
                        setState(() {
                          _wishlist = wishlist;
                          _formKey = formKey;
                        });
                      },
                    )),
              ],
            ),
          ),
          Padding(
            padding:
                const EdgeInsets.only(top: 18, left: 32, right: 32, bottom: 30),
            child: ElevatedLoadingButton(
                onClick: submit, isLoading: _isLoading, label: 'Create'),
          )
        ],
      ),
    );
  }

  Widget buildCreatedBottomSheet(BuildContext context) {
    return BottomSheetContainer(
      title: '',
      onClose: (val) {
        Navigator.of(context).pop();
      },
      headerBackgroundImage: "images/wishlist_background.png",
      height: 203,
      children: [
        Text(
          '${_wishlist?.type == WishlistType.registry ? 'Registry' : 'Wishlist'} created',
          style: GoogleFonts.inter(
              textStyle:
                  const TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        const SizedBox(
          height: 27,
        ),
        Text(
          _wishlist!.name,
          style: GoogleFonts.inter(
              textStyle:
                  const TextStyle(fontSize: 16, fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        const SizedBox(
          height: 4,
        ),
        Conditional(
          condition: _wishlist?.registryDate != null &&
              _wishlist!.registryDate!.isNotEmpty,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'images/iconly/Light/Calendar_small.png',
                height: 12,
                width: 12,
              ),
              const SizedBox(
                width: 4,
              ),
              Text(
                _wishlist?.registryDate != null
                    ? DateFormat('MMM d, yyyy')
                        .format(DateTime.parse(_wishlist!.registryDate!))
                    : '',
                style: const TextStyle(fontSize: 12),
              )
            ],
          ),
        )
      ],
    );
  }
}
