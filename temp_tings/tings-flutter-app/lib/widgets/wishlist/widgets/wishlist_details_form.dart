import 'package:flutter/material.dart';
import 'package:tings/widgets/product/data/product.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/quantity_selector.dart';
import 'package:tings/widgets/shared/text_input.dart';
import 'package:tings/widgets/wishlist/data/wishlist_item.dart';

class WishlistDetailsForm extends StatefulWidget {
  final WishlistItem? wishlistItem;
  final Product product;
  final Function(String, int) onChange;

  const WishlistDetailsForm(
      {super.key,
      this.wishlistItem,
      required this.product,
      required this.onChange});

  @override
  State<StatefulWidget> createState() => _WishlistDetailsFormState();
}

class _WishlistDetailsFormState extends State<WishlistDetailsForm> {
  final TextEditingController _controller = TextEditingController();
  int _quantity = 1;
  bool showSaveButton = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    setState(() {
      _quantity = widget.wishlistItem?.quantity ?? 1;
      _controller.text = widget.wishlistItem?.note ?? '';
    });
    _controller.addListener(() {
      widget.onChange(_controller.text, _quantity);
    });
    _focusNode.addListener(focusListener);
    super.initState();
  }

  void focusListener() {
    setState(() {
      showSaveButton = _focusNode.hasFocus;
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.removeListener(focusListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextInput(
            focusNode: _focusNode,
            controller: _controller,
            title: 'Note for family and friends',
            placeholder: 'e.g. size, color, etc',
          ),
          Conditional(
              condition: showSaveButton,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      FocusManager.instance.primaryFocus?.unfocus();
                    },
                    child: const Text('Save',
                        style: TextStyle(
                            fontSize: 18,
                            color: Colors.white,
                            fontWeight: FontWeight.bold)),
                  ),
                ],
              )),

          const SizedBox(height: 24),
          QuantitySelector(
            value: _quantity,
            title: 'Quantity wanted',
            onChange: (value) {
              if (value > 0) {
                setState(() {
                  _quantity = value;
                });
                widget.onChange(_controller.text, _quantity);
              }
            },
          ),
        ],
      ),
    );
  }
}
