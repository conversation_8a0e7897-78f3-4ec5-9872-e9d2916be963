import 'package:debounce_throttle/debounce_throttle.dart';
import 'package:ensure_visible_when_focused/ensure_visible_when_focused.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/shared/text_input.dart';
import 'package:tings/widgets/wishlist/data/delivery_address.dart';

class DeliveryForm extends StatefulWidget {
  final Function(DeliveryAddress?) onChange;
  final DeliveryAddress? initialValue;
  final GlobalKey<FormState> formKey;

  const DeliveryForm(
      {super.key,
      required this.onChange,
      this.initialValue,
      required this.formKey});

  @override
  State<StatefulWidget> createState() => _DeliveryFormState();
}

class _DeliveryFormState extends State<DeliveryForm> {
  final FocusNode _streetFocusNode = FocusNode();
  final TextEditingController _streetController = TextEditingController();

  final FocusNode _unitFocusNode = FocusNode();
  final TextEditingController _unitController = TextEditingController();

  final FocusNode _cityFocusNode = FocusNode();
  final TextEditingController _cityController = TextEditingController();

  final FocusNode _stateFocusNode = FocusNode();
  final TextEditingController _stateController = TextEditingController();

  final FocusNode _zipFocusNode = FocusNode();
  final TextEditingController _zipController = TextEditingController();

  final debouncer = Debouncer<DeliveryAddress?>(
      const Duration(milliseconds: 400),
      initialValue: null);

  void initState() {
    setState(() {
      if (widget.initialValue != null) {
        _streetController.text = widget.initialValue!.street ?? '';
        _unitController.text = widget.initialValue!.unit ?? '';
        _cityController.text = widget.initialValue!.city ?? '';
        _stateController.text = widget.initialValue!.state ?? '';
        _zipController.text = widget.initialValue!.zip ?? '';
      }
    });
    _streetController.addListener(_onChange);
    _unitController.addListener(_onChange);
    _cityController.addListener(_onChange);
    _stateController.addListener(_onChange);
    _zipController.addListener(_onChange);
    debouncer.values.listen(widget.onChange);
    super.initState();
  }

  void _onChange() {
    if (widget.formKey.currentState!.validate()) {
      debouncer.value = DeliveryAddress(
          city: _cityController.text,
          unit: _unitController.text,
          state: _stateController.text,
          street: _streetController.text,
          zip: _zipController.text);
    }
  }

  bool isDeliveryFormFilled() {
    var somethingFilled = _cityController.text.isNotEmpty ||
        _stateController.text.isNotEmpty ||
        _streetController.text.isNotEmpty ||
        _zipController.text.isNotEmpty;
    if (somethingFilled) {
      return _cityController.text.isEmpty ||
          _stateController.text.isEmpty ||
          _streetController.text.isEmpty ||
          _zipController.text.isEmpty;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 24, right: 24, top: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Delivery address (Optional)',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(fontSize: 12)),
                  textAlign: TextAlign.start),
              const SizedBox(height: 16),
              EnsureVisibleWhenFocused(
                focusNode: _streetFocusNode,
                child: TextInput(
                  validator: (value) {
                    if (isDeliveryFormFilled() &&
                        _streetController.text.isEmpty) {
                      return 'Street is required';
                    }
                    return null;
                  },
                  focusNode: _streetFocusNode,
                  controller: _streetController,
                  placeholder: 'Street address',
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Flexible(
                    child: EnsureVisibleWhenFocused(
                      focusNode: _unitFocusNode,
                      child: TextInput(
                        focusNode: _unitFocusNode,
                        controller: _unitController,
                        placeholder: 'Apt or unit',
                      ),
                    ),
                  ),
                  const SizedBox(width: 11),
                  Flexible(
                    child: EnsureVisibleWhenFocused(
                      focusNode: _cityFocusNode,
                      child: TextInput(
                        validator: (value) {
                          if (isDeliveryFormFilled() &&
                              _cityController.text.isEmpty) {
                            return 'City is required';
                          }
                          return null;
                        },
                        focusNode: _cityFocusNode,
                        controller: _cityController,
                        placeholder: 'City',
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Flexible(
                    child: EnsureVisibleWhenFocused(
                      focusNode: _stateFocusNode,
                      child: TextInput(
                        validator: (value) {
                          if (isDeliveryFormFilled() &&
                              _stateController.text.isEmpty) {
                            return 'State is required';
                          }
                          return null;
                        },
                        focusNode: _stateFocusNode,
                        controller: _stateController,
                        placeholder: 'State',
                      ),
                    ),
                  ),
                  const SizedBox(width: 11),
                  Flexible(
                    child: EnsureVisibleWhenFocused(
                      focusNode: _zipFocusNode,
                      child: TextInput(
                        validator: (value) {
                          if (isDeliveryFormFilled() &&
                              _zipController.text.isEmpty) {
                            return 'Zip is required';
                          }
                          return null;
                        },
                        focusNode: _zipFocusNode,
                        controller: _zipController,
                        placeholder: 'Zip code',
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
