import 'dart:convert';

import 'package:ensure_visible_when_focused/ensure_visible_when_focused.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:textfield_datepicker/textfield_datepicker.dart';
import 'package:tings/globals.dart';
import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/profile/proflie_list.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/custom_switch.dart';
import 'package:tings/widgets/shared/text_input.dart';
import 'package:tings/widgets/wishlist/data/calendar_event.dart';
import 'package:tings/widgets/wishlist/data/delivery_address.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/widgets/collaborators.dart';

import 'delivery_form.dart';

class WishlistForm extends StatefulWidget {
  final Wishlist? wishlist;
  final CalendarEvent? event;
  final bool isRegistry;
  final bool isEvent;
  final Function(Wishlist?, GlobalKey<FormState>) onChange;
  final Function(CalendarEvent?, GlobalKey<FormState>)? onEventChange;

  const WishlistForm(
      {super.key,
      this.wishlist,
      this.event,
      this.onEventChange,
      required this.isRegistry,
      required this.isEvent,
      required this.onChange});

  @override
  State<StatefulWidget> createState() => _WishlistFormState();
}

class _WishlistFormState extends State<WishlistForm> {
  final _formKey = GlobalKey<FormState>();
  FocusNode _focusNodeName = new FocusNode();
  FocusNode _focusNodeDate = new FocusNode();
  List<String> _thumbnails = [];
  String? selectedThumbnail;
  var _private = false;
  DeliveryAddress? _deliveryAddress;

  final TextEditingController _controller = TextEditingController();
  final TextEditingController _dateController = TextEditingController();

  List<Profile> _collaborators = [];

  @override
  void initState() {
    setState(() {
      if (widget.wishlist != null) {
        _controller.text = widget.wishlist!.name;
        if (widget.wishlist!.type == WishlistType.registry) {
          _dateController.text = widget.wishlist!.registryDate!;
        }
        _private = widget.wishlist!.private;
        _collaborators = widget.wishlist!.collaborators;

        selectedThumbnail = widget.wishlist!.iconUrl;
      }
      if (widget.event != null) {
        _controller.text = widget.event!.name;
        _dateController.text = widget.event!.eventDate!;
        selectedThumbnail = widget.event!.iconUrl;
      }
    });
    getDefaultThumbs();

    Future.delayed(const Duration(milliseconds: 200), () {
      widget.onChange(null, _formKey);
      if (widget.onEventChange != null) {
        widget.onEventChange!(null, _formKey);
      }
    });
    _controller.addListener(_onChange);
    _dateController.addListener(_onChange);
    _controller.addListener(_onEventChange);
    _dateController.addListener(_onEventChange);
    super.initState();
  }

  void getDefaultThumbs() async {
    var response = await Api.get('/api/wish_list_icon');
    if (response.statusCode == 200) {
      var items =
          Api.parseList<Thumbnail>(response.bodyBytes, Thumbnail.fromJson);
      items.sort((a, b) => a.orderBy.compareTo(b.orderBy));
      setState(() {
        _thumbnails.clear();
        _thumbnails.addAll(items
            .map<String>((e) => '$cdnUrl/wish_list_icon/${e.url}')
            .toList());
        _thumbnails.add('');
      });
    } else {
      throw Exception('Failed to load thumbnails');
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onChange);
    _dateController.removeListener(_onChange);
    _controller.removeListener(_onEventChange);
    _dateController.removeListener(_onEventChange);
    super.dispose();
  }

  void _onChange() {
    widget.onChange(
        Wishlist(
            name: _controller.text,
            private: _private,
            id: widget.wishlist?.id,
            collaborators: _collaborators,
            deliveryAddress: _deliveryAddress,
            type: widget.isRegistry
                ? WishlistType.registry
                : WishlistType.wishlist,
            registryDate:
                _dateController.text.isNotEmpty ? _dateController.text : null,
            iconUrl: selectedThumbnail ?? _thumbnails[0]),
        _formKey);
  }

  void _onEventChange() {
    if (widget.onEventChange != null) {
      widget.onEventChange!(
          CalendarEvent(
              name: _controller.text,
              id: widget.event?.id,
              eventDate:
                  _dateController.text.isNotEmpty ? _dateController.text : null,
              iconUrl: selectedThumbnail ?? _thumbnails[0]),
          _formKey);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: ListView(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 24, right: 24, bottom: 12),
            child: Text('Thumbnail',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(fontSize: 12))),
          ),
          _ThumbnailCarousel(
            thumbnails: _thumbnails,
            selected: selectedThumbnail,
            onClick: (selected) {
              setState(() {
                selectedThumbnail = selected;
              });
              _onChange();
            },
            onImageDownloaded: (url) {
              setState(() {
                _thumbnails.insert(_thumbnails.length - 1, url);
              });
            },
          ),
          Padding(
            padding:
                const EdgeInsets.only(left: 24, right: 24, top: 32, bottom: 16),
            child: EnsureVisibleWhenFocused(
              focusNode: _focusNodeName,
              child: TextInput(
                focusNode: _focusNodeName,
                controller: _controller,
                keyboardType: TextInputType.multiline,
                minLines: 1,
                maxLines: 5,
                placeholder:
                    '${widget.isEvent ? 'Event' : widget.isRegistry ? 'Registry' : 'Wishlist'} name',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    _focusNodeName.requestFocus();
                    return 'Name is required';
                  }
                  return null;
                },
              ),
            ),
          ),
          Conditional(
              condition: widget.isRegistry || widget.isEvent,
              child: Padding(
                padding: const EdgeInsets.only(left: 18, right: 19),
                child: EnsureVisibleWhenFocused(
                  focusNode: _focusNodeDate,
                  child: TextfieldDatePicker(
                    focusNode: _focusNodeDate,
                    validator: (value) {
                      if ((widget.isRegistry || widget.isEvent) &&
                          (value == null || value.isEmpty)) {
                        _focusNodeDate.requestFocus();
                        return 'Event date is required';
                      }
                      return null;
                    },
                    cupertinoDatePickerBackgroundColor: Colors.white,
                    cupertinoDatePickerMaximumDate: DateTime(2099),
                    cupertinoDatePickerMaximumYear: 2099,
                    cupertinoDatePickerMinimumYear: DateTime.now().year,
                    cupertinoDatePickerMinimumDate: DateTime.now(),
                    cupertinoDateInitialDateTime: DateTime.now(),
                    materialDatePickerFirstDate: DateTime.now(),
                    materialDatePickerInitialDate: widget.event != null
                        ? DateTime.parse(widget.event!.eventDate!)
                        : widget.wishlist?.registryDate != null
                            ? DateTime.parse(widget.wishlist!.registryDate!)
                            : DateTime.now(),
                    materialDatePickerLastDate: DateTime(2099),
                    preferredDateFormat: DateFormat('yyyy-MM-dd'),
                    textfieldDatePickerController: _dateController,
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 18,
                            color: Colors.black,
                            decoration: TextDecoration.none)),
                    textCapitalization: TextCapitalization.sentences,
                    cursorColor: Colors.black,
                    decoration: InputDecoration(
                        labelText: 'Event date',
                        suffixIcon: Transform.scale(
                          scale: 0.5,
                          child: ImageIcon(
                            color: Color(0xFF000000),
                            AssetImage("images/iconly/Light/Calendar.png"),
                          ),
                        )),
                  ),
                ),
              )),
          Conditional(
              condition: !widget.isEvent,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DeliveryForm(
                      formKey: _formKey,
                      onChange: (val) {
                        setState(() {
                          _deliveryAddress = val;
                        });
                        _onChange();
                      },
                      initialValue: widget.wishlist?.deliveryAddress),
                  const SizedBox(height: 32),
                  Padding(
                    padding:
                        const EdgeInsets.only(left: 24, right: 24, top: 16),
                    child: Text('Privacy',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(fontSize: 12))),
                  ),
                  InkWell(
                    onTap: () {
                      setState(() {
                        _private = !_private;
                      });
                      _onChange();
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 8),
                      child: SizedBox(
                        height: 26,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                                'Make this ${widget.isRegistry ? 'registry' : 'wishlist'} private',
                                style: GoogleFonts.inter(
                                    textStyle: const TextStyle(fontSize: 18))),
                            SizedBox(
                              width: 38,
                              child: CustomSwitch(
                                value: _private,
                                onChanged: (bool value) {
                                  setState(() {
                                    _private = value;
                                  });
                                  _onChange();
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  /*
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Text('Collaborators',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(fontSize: 12))),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Conditional(
                      condition: _collaborators.isEmpty,
                      alternate: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: ProfileList(
                          items: _collaborators,
                          buttonLabel: 'Remove',
                          activeButtonLabel: '',
                          onActiveButtonPress: (profile) {},
                          onTap: (profile) {},
                          activeButtonCondition: (profile) => true,
                          onButtonPress: (profile) {
                            setState(() {
                              _collaborators.removeWhere((element) =>
                                  element.username == profile.username);
                            });
                            _onChange();
                          },
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Text(
                            'Allow others to collaborate on your ${widget.isRegistry ? 'registry' : 'wishlist'}',
                            style: GoogleFonts.inter(
                                textStyle: const TextStyle(fontSize: 18))),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                    child: Container(
                      alignment: Alignment.topLeft,
                      child: SizedBox(
                        width: 129,
                        height: 31,
                        child: OutlinedButton(
                          onPressed: () {
                            showModalBottomSheet<void>(
                              context: context,
                              isScrollControlled: true,
                              barrierColor: Colors.black87,
                              builder: (context) {
                                return Collaborators(
                                    collaborators: _collaborators,
                                    onClose: (list) {
                                      setState(() {
                                        _collaborators = list;
                                      });
                                      _onChange();
                                    });
                              },
                            );
                          },
                          style: const ButtonStyle(
                              padding: MaterialStatePropertyAll(
                                  EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 8))),
                          child: Text(
                            'Add collaborator',
                            style: GoogleFonts.inter(
                                textStyle: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600)),
                          ),
                        ),
                      ),
                    ),
                  ),*/
                ],
              )),
        ],
      ),
    );
  }
}

class _ThumbnailCarousel extends StatelessWidget {
  final List<String> thumbnails;
  final Function(String) onClick;
  final Function(String) onImageDownloaded;
  final String? selected;
  final ImagePicker _picker = ImagePicker();

  _ThumbnailCarousel(
      {super.key,
      required this.thumbnails,
      required this.onClick,
      this.selected,
      required this.onImageDownloaded});

  void _pickImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      var uri = Uri(
        scheme: apiScheme,
        host: apiHost,
        port: apiPort,
        path: '/api/upload/AVATAR',
      );
      var request = new http.MultipartRequest("POST", uri);
      request.files.add(await http.MultipartFile.fromPath(
        'file',
        image.path,
      ));
      var response = await request.send();
      if (response.statusCode == 200) {
        response.stream.transform(utf8.decoder).listen((value) async {
          var url = jsonDecode(value)['cdnPath'];
          onImageDownloaded(url);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 80,
      child: Conditional(
          condition: thumbnails.isNotEmpty,
          child: ListView.separated(
              padding: EdgeInsets.symmetric(horizontal: 24),
              itemBuilder: (context, index) {
                print(thumbnails[index]);
                return Conditional(
                  condition: index != thumbnails.length - 1,
                  alternate: InkWell(
                    onTap: _pickImage,
                    child: Container(
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(90)),
                        border: Border.all(
                            color: const Color(0xFFDFDEDA), width: 2),
                        color: const Color(0xFFF5F5F5),
                      ),
                      width: 80,
                      height: 80,
                      child: Center(
                        child: Text('Add\nphoto',
                            style: GoogleFonts.inter(
                                textStyle: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                    decoration: TextDecoration.none)),
                            textAlign: TextAlign.center),
                      ),
                    ),
                  ),
                  child: GestureDetector(
                    onTap: () {
                      onClick(thumbnails[index]);
                    },
                    child: Container(
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(40)),
                        border: Border.all(
                            color: selected == thumbnails[index]
                                ? const Color(0xFFFFC107)
                                : Colors.transparent,
                            width: 3),
                      ),
                      width: 80,
                      height: 80,
                      child: CircleAvatar(
                        minRadius: 80,
                        backgroundImage: NetworkImage(thumbnails[index]),
                      ),
                    ),
                  ),
                );
              },
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              separatorBuilder: (context, index) => const SizedBox(width: 16),
              itemCount: thumbnails.length)),
    );
  }
}

class Thumbnail {
  final String id;
  final String url;
  final int orderBy;

  Thumbnail({
    required this.id,
    required this.url,
    required this.orderBy,
  });

  factory Thumbnail.fromJson(Map<String, dynamic> json) {
    return Thumbnail(
        id: json['id'], url: json['url'], orderBy: json['orderBy']);
  }
}
