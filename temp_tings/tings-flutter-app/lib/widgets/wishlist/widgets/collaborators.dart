import 'dart:async';

import 'package:flutter/material.dart';
import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/profile/proflie_list.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';
import 'package:tings/widgets/shared/conditional.dart';

class Collaborators extends StatefulWidget {
  final List<Profile> collaborators;
  final Function(List<Profile>) onClose;

  const Collaborators(
      {super.key, required this.collaborators, required this.onClose});

  @override
  State<StatefulWidget> createState() => _CollaboratorsState();
}

class _CollaboratorsState extends State<Collaborators> {
  final TextEditingController _controller = TextEditingController();
  bool _showClearButton = false;
  bool _isLoading = false;
  FocusNode? _focusNode;
  Timer? _debounce;

  late List<Profile> existingCollaborators;
  List<Profile> searchResults = [];

  @override
  void initState() {
    setState(() {
      existingCollaborators = widget.collaborators;
    });
    _controller.addListener(() {
      setState(() {
        _showClearButton = _controller.text.isNotEmpty;
      });

      if (_debounce?.isActive ?? false) _debounce?.cancel();
      _debounce = Timer(const Duration(milliseconds: 500), () {
        search(_controller.text);
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void search(String term) async {
    if (mounted) {
      if (term.isEmpty) {
        setState(() {
          searchResults.clear();
          searchResults.addAll(existingCollaborators);
        });
      } else {
        var response = await Api.get('/api/profile/search',
            queryParameters: {'query': term});
        if (response.statusCode == 200) {
          var results =
              Api.parseList<Profile>(response.bodyBytes, Profile.fromJson);
          setState(() {
            searchResults.clear();
            searchResults.addAll(existingCollaborators.where((element) =>
                element.displayName
                    .toLowerCase()
                    .contains(term.toLowerCase())));
            searchResults.addAll(results);
          });
        } else if (response.statusCode == 400) {
          setState(() {
            searchResults.clear();
            searchResults.addAll(existingCollaborators.where((element) =>
                element.displayName
                    .toLowerCase()
                    .contains(term.toLowerCase())));
          });
        } else {
          throw Exception('Failed to search profiles');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheetContainer(
      title: 'Add collaborator',
      onClose: (val) {
        Navigator.of(context).pop();
      },
      children: [
        Padding(
          padding:
              const EdgeInsets.only(bottom: 16, left: 16, right: 16, top: 20),
          child: Row(
            children: [
              Expanded(
                child: Stack(
                  children: [
                    TextField(
                      controller: _controller,
                      onEditingComplete: () {
                        print('complete');
                      },
                      autofocus: true,
                      decoration: const InputDecoration(
                        contentPadding: EdgeInsets.zero,
                        filled: true,
                        fillColor: Color(0xFFFAFAFA),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(70)),
                          borderSide: BorderSide(
                              color: Color(0xFFDDDDDD),
                              width: 1,
                              style: BorderStyle.solid),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(70)),
                          borderSide: BorderSide(
                              color: Color(0xFF484848),
                              width: 1,
                              style: BorderStyle.solid),
                        ),
                        labelText: 'Search...',
                        floatingLabelBehavior: FloatingLabelBehavior.never,
                        prefixIcon: ImageIcon(
                          color: Color(0xFF959595),
                          AssetImage("images/icons/Search_grey.png"),
                        ),
                      ),
                    ),
                    Positioned(
                      right: 0,
                      child: IconButton(
                          onPressed: () {
                            _controller.clear();
                          },
                          icon: Image.asset('images/icons/x-circle.png',
                              height: 22,
                              width: 22,
                              color: _showClearButton
                                  ? Colors.black
                                  : Colors.transparent)),
                    ),
                    Positioned(
                      right: 55,
                      top: 10,
                      child: Conditional(
                        condition: _isLoading,
                        child: const SizedBox(
                          height: 25,
                          width: 25,
                          child: CircularProgressIndicator(strokeWidth: 3),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: TextButton(
                  style: TextButton.styleFrom(
                    textStyle: const TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                        decoration: TextDecoration.underline),
                  ),
                  onPressed: () {
                    _focusNode?.unfocus();
                    _controller.clear();
                    widget.onClose(existingCollaborators);
                    Navigator.of(context).pop();
                  },
                  child: const Text('Invite',
                      style: TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                          decoration: TextDecoration.underline)),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ProfileList(
            items: searchResults,
            activeButtonLabel: 'Add',
            buttonLabel: 'Remove',
            activeButtonCondition: (profile) => existingCollaborators
                .any((element) => element.username == profile.username),
            onActiveButtonPress: (profile) {
              setState(() {
                existingCollaborators.add(profile);
              });
            },
            onButtonPress: (profile) {
              setState(() {
                existingCollaborators.removeWhere(
                    (element) => element.username == profile.username);
              });
            },
            onTap: (profile) {
              setState(() {
                if (existingCollaborators
                    .any((element) => element.username == profile.username)) {
                  existingCollaborators.removeWhere(
                      (element) => element.username == profile.username);
                } else {
                  existingCollaborators.add(profile);
                }
              });
            },
          ),
        ),
      ],
    );
  }
}
