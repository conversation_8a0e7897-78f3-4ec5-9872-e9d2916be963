import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/data/wishlist_item.dart';
import 'package:transparent_image/transparent_image.dart';

class WishlistList extends StatelessWidget {
  final Function(Wishlist) onClick;
  final List<Wishlist> list;
  final List<WishlistItem>? wishlistItems;
  final bool? dense;

  const WishlistList(
      {super.key,
      required this.onClick,
      required this.list,
      this.dense,
      this.wishlistItems});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: list.length,
      itemBuilder: (context, index) => buildWishlistLine(
          list[index],
          wishlistItems
                  ?.any((element) => element.wishListId == list[index].id) ??
              false),
      separatorBuilder: (context, index) => const SizedBox(
        height: 18,
      ),
    );
  }

  Widget buildWishlistLine(Wishlist wishlist, bool hasHeart) {
    return Container(
      height: dense == true ? 46 : 84,
      child: InkWell(
        onTap: () {
          onClick(wishlist);
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              clipBehavior: Clip.hardEdge,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(4)),
              ),
              child: wishlist.iconUrl != null
                  ? FadeInImage.memoryNetwork(
                      placeholder: kTransparentImage,
                      image: wishlist.iconUrl as String,
                      fit: BoxFit.cover,
                      width: dense == true ? 46 : 76,
                      height: dense == true ? 46 : 76,
                    )
                  : Image.asset(
                      'images/tings_place.png',
                      fit: BoxFit.cover,
                      width: dense == true ? 46 : 76,
                      height: dense == true ? 46 : 76,
                    ) as Widget,
            ),
            const SizedBox(
              width: 16,
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(wishlist.name,
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 18,
                              color: Colors.black,
                              fontWeight: FontWeight.w600)),
                      textAlign: TextAlign.left),
                  Conditional(
                    condition: wishlist.type == WishlistType.registry,
                    child: Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Image.asset(
                            'images/iconly/Light/Calendar_small.png',
                            height: 12,
                            width: 12,
                          ),
                          const SizedBox(
                            width: 4,
                          ),
                          Text(
                            wishlist.registryDate != null
                                ? DateFormat('MMM d, yyyy').format(
                                    DateTime.parse(wishlist.registryDate!))
                                : '',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.black,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  Conditional(
                    condition: dense != true,
                    child: Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Row(
                        children: [
                          Conditional(
                            condition: wishlist.private,
                            child: Row(
                              children: [
                                Image.asset(
                                  'images/icons/Lock_small.png',
                                  width: 12,
                                  height: 12,
                                ),
                                Padding(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 4),
                                  child: Text('Private',
                                      style: GoogleFonts.inter(
                                          textStyle: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.black,
                                      )),
                                      textAlign: TextAlign.left),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(right: 4),
                                  child: Text('•',
                                      style: GoogleFonts.inter(
                                          textStyle: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.black,
                                      )),
                                      textAlign: TextAlign.left),
                                )
                              ],
                            ),
                          ),
                          Text('${wishlist.itemCount} items',
                              style: GoogleFonts.inter(
                                  textStyle: const TextStyle(
                                fontSize: 12,
                                color: Colors.black,
                              )),
                              textAlign: TextAlign.left)
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Conditional(condition: hasHeart, child: const Icon(Icons.favorite)),
          ],
        ),
      ),
    );
  }
}
