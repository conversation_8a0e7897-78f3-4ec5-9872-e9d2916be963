import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tings/widgets/product/data/product.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';
import 'package:tings/widgets/shared/loading.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/data/wishlist_item.dart';
import 'package:tings/widgets/wishlist/widgets/wishlist_details_form.dart';
import 'package:tings/widgets/wishlist/widgets/wishlist_list.dart';
import 'package:tings/widgets/wishlist/wishlist_create_form.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';

import '../../shared/event_tracker.dart';
import 'wishlist_info.dart';

class WishlistSelector extends StatefulWidget {
  Product? product;
  List<WishlistItem> initialWishlistItems;
  WishlistSelector(
      {super.key, this.product, required this.initialWishlistItems});

  @override
  State<StatefulWidget> createState() => _WishlistSelectorState();
}

class _WishlistSelectorState extends State<WishlistSelector> with WidgetsBindingObserver {
  final RebuildController controller = RebuildController();
  String _title = '';
  List<Widget> _children = [];
  List<Wishlist> _wishlists = [];
  final List<WishlistItem> _wishlistItems = [];
  double? _height = 200;
  double? _baseHeight = 0;
  double? _marginTop;

  String? _note;
  int _quantity = 1;

  @override
  void initState() {
    init();
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    setState(() {
      _height = _baseHeight! + MediaQuery.of(context).viewInsets.bottom;
    });
  }

  void init() async {
    var prefs = await SharedPreferences.getInstance();
    var wishlistInfoApproved = prefs.getBool('wishlistInfoApproved');
    await getWishlists();

    setState(() {
      _wishlistItems.clear();
      _wishlistItems.addAll(widget.initialWishlistItems);
    });

    if (wishlistInfoApproved != true) {
      showInfoDialog();
    }
    showList();
    await prefs.setBool('wishlistInfoApproved', true);
  }

  void showInfoDialog() {
    showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
            scrollable: true,
            content: WishlistInfo(onClose: (){
              Navigator.of(context).pop();
            },)
        )
    );
  }

  Future getWishlists() async {
    setState(() {
      _title = 'Loading';
      _children = [const Loading(loading: true)];
    });

    var wishlists = await WishlistDataProvider.getWishlistList();
    setState(() {
      _wishlists = wishlists;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheetContainer(
      title: _title,
      onClose: (val) {
        Navigator.of(context).pop();
      },
      height: _height,
      marginTop: _marginTop,
      children: _children,
    );
  }

  void showList() {
    double deviceHeight = MediaQuery.of(context).size.height;
    var padding = MediaQuery.of(context).padding;
    double maxHeight = deviceHeight - padding.top - padding.bottom;

    double height = _wishlists.length * 54 + 250;

    if (height > maxHeight) {
      height = maxHeight;
    }

    setState(() {
      _title = 'Save item';
      _height = height;
      _baseHeight = height;
      _marginTop = null;
      _children = [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Select list',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 14,
                            color: Colors.black,
                            fontWeight: FontWeight.w400)),
                    textAlign: TextAlign.left),
                const SizedBox(
                  height: 20,
                ),
                Expanded(
                  child: RebuildWrapper(
                      controller: controller,
                      child: WishlistList(
                          dense: true,
                          onClick: onWishlistClick,
                          wishlistItems: _wishlistItems,
                          list: _wishlists)),
                ),
                const SizedBox(
                  height: 44,
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      WishlistCreateForm.routeName,
                    ).then((value) async {
                      if (value == true) {
                        await getWishlists();
                      }
                    });
                  },
                  style: const ButtonStyle(
                      padding: MaterialStatePropertyAll(
                          EdgeInsets.symmetric(horizontal: 50, vertical: 15))),
                  child: Text(
                    'Create new list',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 18, fontWeight: FontWeight.w600)),
                  ),
                )
              ],
            ),
          ),
        ),
      ];
    });
  }

  void showForm(Wishlist wishlist) {
    double deviceHeight = MediaQuery.of(context).size.height;
    var padding = MediaQuery.of(context).padding;
    double maxHeight = deviceHeight - padding.top - padding.bottom;

    double height = 475;

    if (height > maxHeight) {
      height = maxHeight;
    }

    setState(() {
      _title = 'Save item';
      _height = height;
      _baseHeight = height;
      _marginTop = null;
      _children = [
        Expanded(
          child: Column(
            children: [
              Text(wishlist.name,
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 14,
                          color: Colors.black,
                          fontWeight: FontWeight.w400)),
                  textAlign: TextAlign.center),
              const SizedBox(
                height: 47,
              ),
              WishlistDetailsForm(
                  product: widget.product!,
                  onChange: (note, quantity) {
                    setState(() {
                      _note = note;
                      _quantity = quantity;
                    });
                  }),
              const SizedBox(
                height: 46,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: ElevatedButton(
                  onPressed: () {
                    onSaveItemClick(wishlist);
                  },
                  style: const ButtonStyle(
                      padding: MaterialStatePropertyAll(
                          EdgeInsets.symmetric(horizontal: 50, vertical: 15))),
                  child: Text(
                    'Save item',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 18, fontWeight: FontWeight.w600)),
                  ),
                ),
              )
            ],
          ),
        ),
      ];
    });
  }

  void onSaveItemClick(Wishlist wishlist) {

    WishlistDataProvider.addItem(widget.product!, wishlist.id!,
        quantity: _quantity, note: _note)
        .then((value) {
          EventTracker().track(
              'Tap Added to Wishlist',
              properties: {
                'product_id': widget.product!.id,
                'product_title': widget.product!.title,
                'wish_list_item_id': value?.id,
                'wish_list_id': wishlist.id
              }
          );


      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            margin: const EdgeInsets.symmetric(
                horizontal: 10, vertical: 10),
            behavior: SnackBarBehavior.floating,
            content: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Image.asset(
                  "images/icons/checkmark.png",
                  width: 16,
                  height: 16,
                ),
                const SizedBox(
                  width: 8,
                ),
                Text('Item saved!',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 16,
                            color: Colors.black,
                            fontWeight: FontWeight.w600))),
              ],
            )),
      );
      Navigator.of(context).pop(true);
    });
  }

  void onWishlistClick(Wishlist wishlist) {
    if (widget.product != null) {
      var index = _wishlistItems
          .indexWhere((element) => element.wishListId == wishlist.id);

      if (index >= 0) {
        WishlistDataProvider.removeItem(_wishlistItems[index].id!);
        setState(() {
          _wishlistItems.removeAt(index);
        });
        if (widget.product != null) {
          widget.product!.inWishList = _wishlistItems.isNotEmpty;
        }
        controller.rebuild();
      } else {
        showForm(wishlist);
      }
    }
  }
}

class RebuildController {
  final GlobalKey rebuildKey = GlobalKey();

  void rebuild() {
    void rebuild(Element el) {
      el.markNeedsBuild();
      el.visitChildren(rebuild);
    }

    (rebuildKey.currentContext as Element).visitChildren(rebuild);
  }
}

class RebuildWrapper extends StatelessWidget {
  final RebuildController controller;
  final Widget child;

  const RebuildWrapper(
      {Key? key, required this.controller, required this.child})
      : super(key: key);

  @override
  Widget build(BuildContext context) => Container(
        key: controller.rebuildKey,
        child: child,
      );
}
