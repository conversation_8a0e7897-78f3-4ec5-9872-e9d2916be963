import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class WishlistInfo extends StatelessWidget {
  final VoidCallback onClose;

  const WishlistInfo({super.key, required this.onClose});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Welcome to your wishlists!',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontWeight: FontWeight.w400)),
            textAlign: TextAlign.left),
        const SizedBox(
          height: 10,
        ),
        Text('Save, collect and organize your Tings!',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.w700)),
            textAlign: TextAlign.left),
        SizedBox(
          height: 35,
        ),
        Row(
          children: [
            Image.asset(
              "images/icons/Lock_yellow.png",
              height: 24,
              width: 24,
            ),
            const SizedBox(
              width: 16,
            ),
            Text('Wishlists can be private',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontWeight: FontWeight.w400)),
                textAlign: TextAlign.left),
          ],
        ),
        const SizedBox(
          height: 16,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              "images/iconly/Light/3_User_yellow.png",
              height: 24,
              width: 24,
            ),
            const SizedBox(
              width: 16,
            ),
            Flexible(
              child: Text(
                  'Or public so your friends and family can buy your Tings',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w400)),
                  textAlign: TextAlign.left),
            ),
          ],
        ),
        const SizedBox(
          height: 16,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              "images/icons/Calendar_yellow.png",
              height: 24,
              width: 24,
            ),
            const SizedBox(
              width: 16,
            ),
            Flexible(
              child: Text(
                  'Create registries for upcoming events and celebrations',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w400)),
                  textAlign: TextAlign.left),
            ),
          ],
        ),
        const SizedBox(
          height: 16,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              "images/icons/add-circle-yellow.png",
              height: 24,
              width: 24,
            ),
            const SizedBox(
              width: 16,
            ),
            Flexible(
              child: Text('Add any items from the web directly to your Tings wishlist',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w400)),
                  textAlign: TextAlign.left),
            ),
          ],
        ),
        Padding(
          padding:
              const EdgeInsets.only(top: 40),
          child: ElevatedButton(
            onPressed: onClose,
            style: const ButtonStyle(
                padding: MaterialStatePropertyAll(
                    EdgeInsets.symmetric(horizontal: 50, vertical: 15))),
            child: Text(
              'Got it!',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.w600)),
            ),
          ),
        )
      ],
    );
  }
}
