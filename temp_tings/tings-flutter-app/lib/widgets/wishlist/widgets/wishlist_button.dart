import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:tings/widgets/auth/auth_bottom_sheet.dart';
import 'package:tings/widgets/product/data/product.dart';
import 'package:tings/widgets/wishlist/data/wishlist_item.dart';
import 'package:tings/widgets/wishlist/widgets/wishlist_selector.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';

class WishlistButton extends StatefulWidget {
  final Product product;
  final Function(bool)? onClose;
  final bool? active;

  const WishlistButton(
      {super.key, required this.product, this.onClose, this.active});

  @override
  State<StatefulWidget> createState() =>
      _WishlistButtonState(active: active ?? product.inWishList == true);
}

class _WishlistButtonState extends State<WishlistButton> {
  bool active;
  late List<WishlistItem> _wishlistItems;

  _WishlistButtonState({required this.active});

  Future loadWishlistItems() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      var items =
          await WishlistDataProvider.getWishlistItemsByProduct(widget.product);
      setState(() {
        _wishlistItems = items;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
        radius: 30,
        onTap: () async {
          await loadWishlistItems();
          showModalBottomSheet<bool?>(
            context: context,
            isScrollControlled: true,
            barrierColor: Colors.black87,
            builder: (context) {
              final user = FirebaseAuth.instance.currentUser;
              if (user != null) {
                return WishlistSelector(
                  product: widget.product,
                  initialWishlistItems: _wishlistItems,
                );
              } else {
                return AuthBottomSheet(onClose: () {});
              }
            },
          ).then((value) {
            if (value != null) {
              widget.product.inWishList = value;
              setState(() {
                active = widget.active ?? value;
              });
              if (widget.onClose != null) {
                widget.onClose!(true);
              }
            } else {
              setState(() {
                active = widget.active ?? widget.product.inWishList == true;
              });
              if (widget.onClose != null) {
                widget.onClose!(true);
              }
            }
          });
        },
        child: Container(
          height: 32,
          width: 32,
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(16)),
            color: Colors.white,
          ),
          child: active
              ? const Icon(Icons.favorite)
              : const Icon(Icons.favorite_outline),
        ));
  }
}
