import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CalendarInfo extends StatelessWidget {
  final VoidCallback onClose;

  const CalendarInfo({super.key, required this.onClose});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Welcome to your calendar!',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontWeight: FontWeight.w400)),
            textAlign: TextAlign.left),
        const SizedBox(
          height: 10,
        ),
        Text('Never miss a holiday or special occasion again!',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.w700)),
            textAlign: TextAlign.left),
        const SizedBox(
          height: 35,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              "images/icons/Calendar_yellow.png",
              height: 24,
              width: 24,
            ),
            const SizedBox(
              width: 16,
            ),
            Flexible(
              child: Text(
                  'Your calendar is preset with annual holidays and birthdays (of your friends on Tings!)',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w400)),
                  textAlign: TextAlign.left),
            ),
          ],
        ),
        const SizedBox(
          height: 16,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              "images/icons/add-circle-yellow.png",
              height: 24,
              width: 24,
            ),
            const SizedBox(
              width: 16,
            ),
            Flexible(
              child: Text(
                  'Is there a special date you don\'t want to forget? Click the plus icon to add your own events to your Tings calendar',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w400)),
                  textAlign: TextAlign.left),
            ),
          ],
        ),
        Padding(
          padding:
              const EdgeInsets.only(top: 40),
          child: ElevatedButton(
            onPressed: onClose,
            style: const ButtonStyle(
                padding: MaterialStatePropertyAll(
                    EdgeInsets.symmetric(horizontal: 50, vertical: 15))),
            child: Text(
              'Got it!',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.w600)),
            ),
          ),
        )
      ],
    );
  }
}
