import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:tings/widgets/friends/friend_card.dart';
import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/profile/profile_avatar.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/wishlist/calendar_event_edit_form.dart';
import 'package:tings/widgets/wishlist/data/calendar_event.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';

import '../shared/conditional.dart';
import 'data/upcoming_event.dart';
import 'wishlist_items_list.dart';

class UpcomingEvents extends StatefulWidget {
  const UpcomingEvents({super.key});

  @override
  State<StatefulWidget> createState() => _UpcomingEventsState();
}

class _UpcomingEventsState extends State<UpcomingEvents> {
  final List<UpcomingEvent> _items = [];
  bool _loading = true;

  @override
  void initState() {
    init();
    super.initState();
  }

  void init() async {
    setState(() {
      _loading = true;
      _items.clear();
    });
    var items = await WishlistDataProvider.getUpcomingEvents();

    setState(() {
      _items.addAll(items);
      _loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Conditional(
      condition: _items.isNotEmpty,
      child: SizedBox(
        height: 106,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
              child: Text(
                'Upcoming events',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                        fontWeight: FontWeight.w600)),
                textAlign: TextAlign.left,
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            SizedBox(
              height: 50,
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                shrinkWrap: true,
                itemCount: _items.length,
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, index) {
                  return buildChip(_items[index], index, context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildChip(UpcomingEvent item, int index, BuildContext context) =>
      GestureDetector(
        onTap: () async {
          if (item.id!.startsWith("birthday")) {
            var response = await Api.get('/api/profile/' + item.owner!);

            if (response.statusCode == 200) {
              var profile = Api.parse(response.bodyBytes, Profile.fromJson);
              Navigator.of(context)
                  .push(MaterialPageRoute(
                  builder: (context) => FriendCard(profile: profile)));
            }
          } else if (item.id!.startsWith("calendar_event:PRIVATE")) {
            Navigator.pushNamed(context, CalendarEventEditForm.routeName,
                arguments: CalendarEventEditFormArguments(CalendarEvent(
                    name: item.name,
                    eventDate: item.registryDate,
                    iconUrl: item.avatarUrl,
                    id: item.id!.split(":").last)));
          } else if (item.id!.startsWith("registry")) {
            var id = item.id!.replaceFirst('registry-', '');
            var wishlist = await WishlistDataProvider.getWishlistById(id);
            if (wishlist != null) {
              Navigator.pushNamed(
                context,
                WishlistItemsList.routeName,
                arguments: WishlistItemsListArguments(wishlist, false),
              );
            }
          }
          /*
        Navigator.pushNamed(
          context,
          ProductView.routeName,
          arguments: ProductViewArguments(product),
        );
      */
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 3),
          child: EventChip(
            item: item,
            color: index % 2 == 0
                ? const Color(0xFFFFC107)
                : const Color(0xFF1976D2),
          ),
        ),
      );
}

class EventChip extends StatelessWidget {
  final Color? color;
  final UpcomingEvent item;

  const EventChip({super.key, this.color, required this.item});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      clipBehavior: Clip.hardEdge,
      padding: const EdgeInsets.only(left: 5, top: 5, bottom: 5, right: 30),
      decoration: BoxDecoration(
        color: color != null ? color?.withAlpha(20) : const Color(0xFFF6F6F6),
        borderRadius: const BorderRadius.all(Radius.circular(90)),
      ),
      child: Row(
        children: [
          ProfileAvatar(
            avatarUrl: item.avatarUrl,
            displayName: item.name,
            radius: 20,
            color: color,
            labelColor: Colors.white,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(item.name,
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 14, fontWeight: FontWeight.w500)),
                    textAlign: TextAlign.left),
                const SizedBox(
                  height: 2,
                ),
                Text(
                    DateFormat('MMM d')
                        .format(DateTime.parse(item.registryDate!)),
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(fontSize: 10)),
                    textAlign: TextAlign.left)
              ],
            ),
          )
        ],
      ),
    );
  }
}
