import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/elevated_loading_button.dart';
import 'package:tings/widgets/shared/styled_tabs.dart';
import 'package:tings/widgets/wishlist/data/calendar_event.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';

import '../shared/event_tracker.dart';
import 'widgets/wishlist_form.dart';

class CalendarEventCreateForm extends StatefulWidget {
  const CalendarEventCreateForm({super.key});
  static const String routeName = '/create-calendar-event';

  @override
  State<StatefulWidget> createState() => _CalendarEventCreateFormState();
}

class _CalendarEventCreateFormState extends State<CalendarEventCreateForm> {
  Wishlist? _wishlist;
  CalendarEvent? _event;
  bool _isLoading = false;

  GlobalKey<FormState>? _formKey;

  @override
  void initState() {
    EventTracker().track('View New Event');
    super.initState();
  }

  void submit() async {
    if (_formKey != null && _formKey!.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });
      if (_wishlist != null) {
        var response = await WishlistDataProvider.addWishlist(_wishlist!);
        if (response != null) {
          showModalBottomSheet<void>(
            context: context,
            isScrollControlled: true,
            barrierColor: Colors.black87,
            builder: buildCreatedBottomSheet,
          ).then((value) => Navigator.of(context).pop(true));
        } else {
          throw Exception('Failed to save wishlist');
        }
      } else if (_event != null) {
        var response =
            await Api.post('/api/calendar_event', body: _event!.toJson());
        if (response.statusCode == 200) {
          showModalBottomSheet<void>(
            context: context,
            isScrollControlled: true,
            barrierColor: Colors.black87,
            builder: buildCreatedBottomSheet,
          ).then((value) => Navigator.of(context).pop(true));
        } else {
          throw Exception('Failed to save calendar event');
        }
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'New event',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Image.asset(
              "images/icons/clear.png",
              width: 18,
              height: 18,
            ),
            color: Colors.black,
          )
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: StyledTabs(
              tabs: [
                StyledTab(
                    title: 'Calendar only',
                    widget: WishlistForm(
                      isRegistry: false,
                      isEvent: true,
                      onChange: (wishlist, formKey) {},
                      onEventChange: (event, formKey) {
                        setState(() {
                          _event = event;
                          _formKey = formKey;
                        });
                      },
                    )),
                StyledTab(
                    title: 'Registry',
                    widget: WishlistForm(
                      isRegistry: true,
                      isEvent: false,
                      onChange: (wishlist, formKey) {
                        setState(() {
                          _wishlist = wishlist;
                          _formKey = formKey;
                        });
                      },
                    )),
              ],
            ),
          ),
          Padding(
            padding:
                const EdgeInsets.only(top: 18, left: 32, right: 32, bottom: 30),
            child: ElevatedLoadingButton(
                onClick: submit, isLoading: _isLoading, label: 'Create'),
          )
        ],
      ),
    );
  }

  Widget buildCreatedBottomSheet(BuildContext context) {
    String type =
        _wishlist?.type == WishlistType.registry ? 'Registry' : 'Wishlist';
    if (_event != null) {
      type = 'Event';
    }
    String name = _event != null ? _event!.name : _wishlist!.name;
    String? date;
    if (_wishlist?.registryDate != null &&
        _wishlist!.registryDate!.isNotEmpty) {
      date = DateFormat('MMM d, yyyy')
          .format(DateTime.parse(_wishlist!.registryDate!));
    }
    if (_event?.eventDate != null && _event!.eventDate!.isNotEmpty) {
      date =
          DateFormat('MMM d, yyyy').format(DateTime.parse(_event!.eventDate!));
    }
    return BottomSheetContainer(
      title: '',
      onClose: (val) {
        Navigator.of(context).pop();
      },
      headerBackgroundImage: "images/wishlist_background.png",
      height: 203,
      children: [
        Text(
          '$type created',
          style: GoogleFonts.inter(
              textStyle:
                  const TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        const SizedBox(
          height: 27,
        ),
        Text(
          name,
          style: GoogleFonts.inter(
              textStyle:
                  const TextStyle(fontSize: 16, fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        const SizedBox(
          height: 4,
        ),
        Conditional(
          condition: date != null,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'images/iconly/Light/Calendar_small.png',
                height: 12,
                width: 12,
              ),
              const SizedBox(
                width: 4,
              ),
              Text(
                date ?? '',
                style: const TextStyle(fontSize: 12),
              )
            ],
          ),
        )
      ],
    );
  }
}
