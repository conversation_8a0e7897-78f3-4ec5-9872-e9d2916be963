import 'package:tings/globals.dart';

class Category {
  final String id;
  final String name;
  final String? imageUrl;
  final bool hidden;
  final String type;

  Category({
    required this.id,
    required this.name,
    this.imageUrl,
    required this.hidden,
    required this.type,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    String? imageUrl;
    if (json['imageUrl'] != null) {
      imageUrl = '$cdnUrl/category/${json['imageUrl']}';
    }

    return Category(
      id: json['id'],
      name: json['name'],
      type: json['type'],
      imageUrl: imageUrl,
      hidden: json['hidden'],
    );
  }
}

class SubCategory {
  final String id;
  final String name;

  SubCategory({
    required this.id,
    required this.name,
  });

  factory SubCategory.fromJson(Map<String, dynamic> json) {
    return SubCategory(id: json['id'], name: json['name']);
  }
}
