import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tings/widgets/product/parse_product_url_form.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/loading.dart';

class SearchField extends StatelessWidget {
  final Function onCancelClick;
  final bool showCancelButton;
  final bool isLoading;
  final FocusNode focusNode;
  final TextEditingController controller;

  const SearchField({
    super.key,
    required this.showCancelButton,
    required this.onCancelClick,
    required this.focusNode,
    required this.controller,
    required this.isLoading
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, left: 16, right: 16),
      child: Row(
        children: [
          Expanded(
            child: Stack(
              children: [
                TextField(
                  controller: controller,
                  focusNode: focusNode,
                  onEditingComplete: () {
                    SystemChannels.textInput.invokeMethod('TextInput.hide');
                  },
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.only(right: 40),
                    filled: true,
                    fillColor: Color(0xFFFAFAFA),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(70)),
                      borderSide: BorderSide(
                          color: Color(0xFFDDDDDD), width: 1, style: BorderStyle.solid),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(70)),
                      borderSide: BorderSide(
                          color: Color(0xFF484848), width: 1, style: BorderStyle.solid),
                    ),
                    labelText: 'Search...',
                    floatingLabelBehavior: FloatingLabelBehavior.never,
                    prefixIcon: ImageIcon(
                      color: Color(0xFF959595),
                      AssetImage("images/icons/Search_grey.png"),
                    ),
                  ),
                ),
                Positioned(
                  right: 1,
                  top: 7,
                  width: 40,
                  height: 35,
                  child: Container(
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(50)),
                      color: Color(0xFFFAFAFA),
                    ),
                    child: IconButton(
                        onPressed: () {
                          controller.clear();
                        },
                        icon: Image.asset('images/icons/x-circle.png',
                            height: 20,
                            width: 20,
                            color: controller.text.isEmpty
                                ? Colors.transparent
                                : Colors.black)),
                  ),
                ),
                Positioned(
                  right: 1,
                  top: 7,
                  width: 40,
                  height: 35,
                  child: Loading(
                    padding: const EdgeInsets.only(right: 12, bottom: 8, top: 8, left: 8),
                    loading: isLoading,
                    strokeWidth: 2,
                  ),
                ),
              ],
            ),
          ),
          Conditional(
            condition: showCancelButton,
            alternate: IconButton(
                padding: const EdgeInsets.only(left: 8),
                onPressed: () {
                  Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => const ParseProductUrlForm()));
                },
                icon: Image.asset(
                  "images/icons/add-circle.png",
                  width: 24,
                  height: 24,
                )),
            child: Padding(
              padding: const EdgeInsets.only(left: 8),
              child: TextButton(
                style: TextButton.styleFrom(
                  textStyle: const TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                      decoration: TextDecoration.underline),
                ),
                onPressed: () {
                  focusNode.unfocus();
                  controller.clear();
                  onCancelClick();
                },
                child: const Text('Cancel',
                    style: TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                        decoration: TextDecoration.underline)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}