import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/product/data/product.dart';
import 'package:tings/widgets/search/data/search_history_item.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/shimmer_loading.dart';

import '../../product/product_view.dart';
import '../../shared/api.dart';

class SearchResults extends StatelessWidget {
  final ScrollController controller;
  final List<SearchHistoryItem> results;
  final Function(SearchHistoryItem item) onTap;

  const SearchResults({
    super.key,
    required this.results,
    required this.controller,
    required this.onTap
  });


  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Conditional(
        condition: results.isNotEmpty,
        alternate: Center(
          child: Text(
            'No search results found',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.w700)),
            textAlign: TextAlign.center,
          ),
        ),
        child: MasonryGridView.count(
          controller: controller,
          itemCount: results.length,
          padding: const EdgeInsets.only(bottom: 50, left: 16, right: 16),
          // the number of columns
          crossAxisCount: 2,
          // vertical gap between two items
          mainAxisSpacing: 10,
          // horizontal gap between two items
          crossAxisSpacing: 10,
          itemBuilder: (context, index) {
            double deviceWidth = MediaQuery.of(context).size.width;
            var padding = MediaQuery.of(context).padding;
            double colWidth = (deviceWidth - padding.left - padding.right) / 2 - 21;
            var imgHeight = results[index].height?.toDouble() ?? 150;
            var imgWidth = results[index].width?.toDouble() ?? colWidth;
            var ratio = colWidth / imgWidth;
            var height = imgHeight * ratio;

            // display each item with a card
            return GestureDetector(
              onTap: () {
                onTap(results[index]);
                getProduct(results[index]).then((value) {
                  goToProduct(value, context);
                });
              },
              child: Container(
                clipBehavior: Clip.hardEdge,
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4)),
                ),
                child: CachedNetworkImage(
                  imageUrl: results[index].url ?? '',
                  placeholder: (context, url) => ShimmerLoading(
                    linearGradient: shimmerGradient,
                    child: Container(
                        width: colWidth,
                        height: height,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(4),
                        )),
                  ),
                  errorWidget: (context, url, error) => Image.asset(
                    "images/product_placeholder.png",
                    width: 328,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Future<Product> getProduct(SearchHistoryItem item) async {
    final response = await Api.get('/api/product/${item.id}');
    if (response.statusCode == 200) {
      return Product.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load product');
    }
  }

  void goToProduct(Product product, BuildContext context) {
    Navigator.pushNamed(
      context,
      ProductView.routeName,
      arguments: ProductViewArguments(product: product),
    ).then((value) {
      //_controller.clear();
    });
  }

}