import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/product/data/product.dart';
import 'package:tings/widgets/product/product_carousel.dart';

import '../../shared/api.dart';

class RecentlyAdded extends StatefulWidget {

  const RecentlyAdded({
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _RecentlyAddedState();
}

class _RecentlyAddedState extends State<RecentlyAdded> {
  final List<Product> _products = [];

  @override
  void initState() {
    _fetchProducts();
    super.initState();
  }

  void _fetchProducts() async {
    final response = await Api.get('/api/product/search', queryParameters: {
      'pageSize': '20',
      'page': '0',
      'sorting': 'newItems'
    });

    if (response.statusCode == 200) {
      setState(() {
        _products.addAll(
            Api.parseList<Product>(response.bodyBytes, Product.fromJson));
      });
    } else {
      throw Exception('Failed to load products');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 16, bottom: 9, left: 16),
          child: Text('Recently added',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold)),
              textAlign: TextAlign.start),
        ),
        ProductsCarousel(products: _products),
      ],
    );
  }
}