import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/product/product_list.dart';

import '../../shared/api.dart';
import '../data/category.dart';

class DefaultCategories extends StatefulWidget {
  const DefaultCategories({
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _DefaultCategoriesState();
}

class _DefaultCategoriesState extends State<DefaultCategories> {
  final List<Category> _categories = [];

  @override
  void initState() {
    _fetchCategories();
    super.initState();
  }

  void _fetchCategories() async {
    final response = await Api.get('/api/category');

    if (response.statusCode == 200) {
      setState(() {
        var categories =
            Api.parseList<Category>(response.bodyBytes, Category.fromJson);
        _categories
            .addAll(categories.where((element) => element.type == 'DEFAULT'));
        // _categories.sort((a, b) {
        //   // Check if name starts with 'e' or 'E' for both categories
        //   bool startswitheA = a.name.toLowerCase().startsWith('e');
        //   bool startswitheB = b.name.toLowerCase().startsWith('e');

        //   if (startswitheA && !startswitheB) {
        //     // a starts with 'e' but b doesn't, a should come first
        //     return -1;
        //   } else if (!startswitheA && startswitheB) {
        //     // b starts with 'e' but a doesn't, b should come first
        //     return 1;
        //   } else {
        //     // If both start with 'e' or neither do, maintain existing order
        //     return 0;
        //   }
        // });
      });
    } else {
      throw Exception('Failed to load categories');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 27, bottom: 9, left: 16),
          child: Text('Popular on Tings',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold)),
              textAlign: TextAlign.start),
        ),
        GridView.count(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            shrinkWrap: true,
            primary: false,
            crossAxisSpacing: 11,
            mainAxisSpacing: 11,
            crossAxisCount: 2,
            childAspectRatio: 2.3,
            children:
                _categories.map((i) => buildCatItem(i, context)).toList()),
      ],
    );
  }

  Widget buildCatItem(Category category, BuildContext context) =>
      GestureDetector(
        onTap: () {
          onCategoryTap(category, context);
        },
        child: Container(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(4)),
            color: Colors.black,
          ),
          clipBehavior: Clip.hardEdge,
          child: Center(
              child: Text(category.name,
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w500)))),
        ),
      );

  void onCategoryTap(Category category, BuildContext context) {
    Navigator.pushNamed(
      context,
      ProductList.routeName,
      arguments: ProductListArguments(
        category: category.id,
        showBackButton: true,
        titleText: category.name,
        title: Text(category.name,
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.bold)),
            textAlign: TextAlign.center),
        centerTitle: true,
      ),
    );
  }
}
