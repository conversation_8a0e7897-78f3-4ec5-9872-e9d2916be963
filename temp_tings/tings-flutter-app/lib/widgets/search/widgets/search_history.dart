import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class SearchHistoryList extends StatelessWidget {
  final Function(String item) onItemTap;
  final Function() onClearTap;
  final Function(String item) onItemDeleteTap;
  final List<String> items;

  const SearchHistoryList({
    super.key,
    required this.onItemTap,
    required this.onClearTap,
    required this.onItemDeleteTap,
    required this.items
  });

 @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          children: [
            SizedBox(
              height: 40,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Text(
                      'Recent',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 18,
                              color: Colors.black,
                              fontWeight: FontWeight.w700)),
                      textAlign: TextAlign.start,
                    ),
                    const SizedBox(width: 9),
                    GestureDetector(
                      onTap: onClearTap,
                      child: const Text('Clear history',
                          style: TextStyle(
                              fontSize: 16,
                              color: Colors.black,
                              decoration: TextDecoration.underline)),
                    )
                  ],
                ),
              ),
            ),
            Expanded(
              child: ListView(
                children: items
                    .map(buildSearchHistoryItemLine)
                    .toList(),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget buildSearchHistoryItemLine(String item) => SizedBox(
    height: 40,
    child: Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () {
              onItemTap(item);
            },
            child: Text(item,
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 18, color: Colors.black))),
          ),
        ),
        IconButton(
          onPressed: () {
            onItemDeleteTap(item);
          },
          icon: const Icon(Icons.close, size: 24),
          color: Colors.black,
        )
      ],
    ),
  );

}