import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/product/product_list.dart';
import 'package:transparent_image/transparent_image.dart';

import '../../shared/api.dart';
import '../../shared/conditional.dart';
import '../data/category.dart';

class VerticalCategories extends StatefulWidget {
  const VerticalCategories({
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _VerticalCategoriesState();
}

class _VerticalCategoriesState extends State<VerticalCategories> {
  final List<Category> _categories = [];

  @override
  void initState() {
    _fetchCategories();
    super.initState();
  }

  void _fetchCategories() async {
    final response = await Api.get('/api/category');

    if (response.statusCode == 200) {
      setState(() {
        var categories =
        Api.parseList<Category>(response.bodyBytes, Category.fromJson);
        _categories
            .addAll(categories.where((element) => element.type == 'VERTICAL'));
      });
    } else {
      throw Exception('Failed to load categories');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: ClampingScrollPhysics(),
      padding: EdgeInsets.all(16),
      itemCount: _categories.length,
      itemBuilder: (context, index) =>buildCatImage(_categories[index], context),
      separatorBuilder: (context, index) => const SizedBox(height: 16),
    );
  }

  Widget buildCatImage(Category category, BuildContext context) =>
      GestureDetector(
        onTap: () {
          onCategoryTap(category, context);
        },
        child: Container(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          clipBehavior: Clip.hardEdge,
          child: category.imageUrl != null
              ? FadeInImage.memoryNetwork(
            placeholder: kTransparentImage,
            image: category.imageUrl as String,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width - MediaQuery.of(context).size.width * .05,
            height: 200,
          )
              : Image.asset(
            'images/no-image-found.png',
            fit: BoxFit.cover,
            width: 400,
            height: 200,
          ) as Widget,
        ),
      );

  void onCategoryTap(Category category, BuildContext context) {
    Navigator.pushNamed(
      context,
      ProductList.routeName,
      arguments: ProductListArguments(
        category: category.id,
        showBackButton: true,
        titleText: category.name,
        title: Text(category.name,
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.bold)),
            textAlign: TextAlign.center),
        centerTitle: true,
      ),
    );
  }
}