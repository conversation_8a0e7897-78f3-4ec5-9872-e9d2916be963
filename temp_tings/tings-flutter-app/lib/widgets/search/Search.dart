import 'dart:async';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tings/widgets/search/data/search_history_item.dart';
import 'package:tings/widgets/search/widgets/search_history.dart';
import 'package:tings/widgets/search/widgets/search_results.dart';
import 'package:tings/widgets/search/widgets/special_categories.dart';
import 'package:tings/widgets/search/widgets/vertical_categories.dart';
import 'package:tings/widgets/shared/api.dart';

import '../shared/event_tracker.dart';
import '../shared/conditional.dart';
import '../wishlist/upcoming_events.dart';
import 'widgets/default_categories.dart';
import 'widgets/recently_added.dart';
import 'widgets/search_field.dart';

class Search extends StatefulWidget {
  const Search({super.key});

  @override
  State<StatefulWidget> createState() => _SearchState();
}

class _SearchState extends State<Search> {
  final String _searchHistoryKey = 'search_history';
  String prev = '';
  final ScrollController _scrollController = ScrollController();
  SearchViewType type = SearchViewType.Main;
  final List<String> _searchHistory = [];
  bool _isLoading = false;
  final List<SearchHistoryItem> _searchResults = List.empty(growable: true);
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  Timer? _debounce;

  @override
  void initState() {
    EventTracker().track('View Search');
    setState(() {
      _searchResults.clear();
      _isLoading = false;

      SharedPreferences.getInstance().then((value) {
        var val = value.getStringList(_searchHistoryKey);
        if (val != null) {
          setState(() {
            _searchHistory.addAll(val);
          });
        }
      });
    });

    _focusNode.removeListener(focusListener);
    _focusNode.addListener(focusListener);

    _controller.addListener(() {
      if (_debounce?.isActive ?? false) {
        _debounce?.cancel();
      }
      _debounce = Timer(const Duration(milliseconds: 500), () {
        if (prev == _controller.text) {
          return;
        }
        prev = _controller.text;
        if (_controller.text.length > 2) {
          _search(_controller.text);
        } else {
          if (type == SearchViewType.Results) {
            setState(() {
              type = SearchViewType.History;
            });
          }
        }
      });
    });

    super.initState();
  }

  Future _search(String query) async {
    setState(() {
      _isLoading = true;
    });
    final response = await Api.get('/api/product-autocomplete',
        queryParameters: {'query': query});
    setState(() {
      _isLoading = false;
    });
    if (response.statusCode == 200) {
      final parsed =
      Api.parseList(response.bodyBytes, SearchHistoryItem.fromJson);
      setState(() {
        _searchResults.clear();
        _searchResults.addAll(parsed);
        type = SearchViewType.Results;
      });
    } else {
      throw Exception('Failed to load autocomplete items');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 50),
      child: Column(children: [
        SearchField(
          focusNode: _focusNode,
          controller: _controller,
          isLoading: _isLoading,
          showCancelButton: type != SearchViewType.Main,
          onCancelClick: () {
            setState(() {
              type = SearchViewType.Main;
            });
          },
        ),
        Conditional(condition: type == SearchViewType.Main, child: const MainView()),
        Conditional(
            condition: type == SearchViewType.Results,
            child: SearchResults(
              results: _searchResults,
              controller: _scrollController,
              onTap: (item) {
                addSearchToHistory(item.title);
              },
            )
        ),
        Conditional(
            condition: type == SearchViewType.History,
            child: SearchHistoryList(
              onItemTap: (item) {
                _controller.text = item;
                _focusNode.requestFocus();
              },
              items: _searchHistory,
              onClearTap: clearHistory,
              onItemDeleteTap: deleteSearchHistoryItem,
            ),
        ),
      ]),
    );
  }

  Future addSearchToHistory(String item) async {
    if (item != '') {
      var prefs = await SharedPreferences.getInstance();
      setState(() {
        _searchHistory.removeWhere((element) => element == item);
        _searchHistory.insert(0, item);
      });
      var t = _searchHistory.toList();
      await prefs.setStringList(_searchHistoryKey, t);
    }
  }

  Future clearHistory() async {
    var prefs = await SharedPreferences.getInstance();
    setState(() {
      _searchHistory.clear();
    });
    await prefs.remove(_searchHistoryKey);
  }


  Future deleteSearchHistoryItem(String item) async {
    var prefs = await SharedPreferences.getInstance();
    setState(() {
      _searchHistory.removeWhere((element) => element == item);
    });
    await prefs.setStringList(_searchHistoryKey, _searchHistory.toList());
  }

  void focusListener() {
    setState(() {
      if (_focusNode.hasFocus && type == SearchViewType.Main) {
        type = SearchViewType.History;
      }
    });
  }
}

enum SearchViewType {
  Main,
  History,
  Results
}

class MainView extends StatelessWidget {
  const MainView({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: ListView(
        shrinkWrap: true,
        physics: ClampingScrollPhysics(),
        children: const [
          SpecialCategories(),
          UpcomingEvents(),
          RecentlyAdded(),
          DefaultCategories(),
          VerticalCategories(),
        ],
      ),
    );
  }

}

