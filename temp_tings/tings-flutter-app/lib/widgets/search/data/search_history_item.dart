import 'dart:convert';

import 'package:tings/globals.dart';

class SearchHistoryItem {
  final String id;
  final String title;
  final String? url;
  final int? height;
  final int? width;

  SearchHistoryItem({
    this.url,
    this.height,
    this.width,
    required this.id,
    required this.title
  });

  factory SearchHistoryItem.fromJson(Map<String, dynamic> json) {
    return SearchHistoryItem(
      id: json['id'],
      title: json['title'],
      height: json['height'],
      width: json['width'],
      url: json['url'] != null ? '$cdnUrl/image/${json['url']}' : null,
    );
  }

  String stringify() {
    return jsonEncode({'id': id, 'title': title, 'height': height, 'width': width, 'url': url});
  }

  @override
  String toString() {
    return title;
  }
}
