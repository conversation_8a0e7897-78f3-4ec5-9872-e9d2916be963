import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/product/product_filter.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/gender.dart';

import '../brand/brand.dart';
import '../shared/event_tracker.dart';
import '../shared/checkbox_option.dart';
import '../shared/loading.dart';
import '../shared/radio_option.dart';

class ProductFilterForm extends StatefulWidget {
  final Function(Map<String, dynamic>?) onClose;
  final Map<String, dynamic> initial;

  const ProductFilterForm({super.key, required this.initial, required this.onClose});

  @override
  State<StatefulWidget> createState() => _ProductFilterFormState();
}

class _ProductFilterFormState extends State<ProductFilterForm> {
  Map<String, dynamic> _filters = new Map();

  @override
  void initState() {
    EventTracker().track('View Filter');
    setState(() {
      _filters.clear();
      _filters.addAll(widget.initial);
    });
    super.initState();
  }

  Future<List<Brand>> _fetchBrands() async {
    final response = await Api.get('/api/brand-list');

    if (response.statusCode == 200) {
      var list = Api.parseList<Brand>(response.bodyBytes, Brand.fromJson);
      list.sort((a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()));
      return list;
    } else {
      throw Exception('Failed to load brands');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: CustomScrollView(
                slivers: [
                  SliverList(
                    delegate: SliverChildListDelegate([
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Text('Sort by',
                            style: GoogleFonts.inter(
                                textStyle: TextStyle(
                                    fontSize: 14,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600))),
                      ),
                      RadioOption<String>(
                          groupValue: _filters[ProductFilterKey.sorting.value],
                          onChanged: (value) => setState(() {
                                _filters[ProductFilterKey.sorting.value] =
                                    value;
                              }),
                          text: 'Most popular',
                          value: Sorting.mostPopular.value),
                      RadioOption<String>(
                          groupValue: _filters[ProductFilterKey.sorting.value],
                          onChanged: (value) => setState(() {
                                _filters[ProductFilterKey.sorting.value] =
                                    value;
                              }),
                          text: 'What\'s new',
                          value: Sorting.newItems.value),
                      RadioOption<String>(
                          groupValue: _filters[ProductFilterKey.sorting.value],
                          onChanged: (value) => setState(() {
                                _filters[ProductFilterKey.sorting.value] =
                                    value;
                              }),
                          text: 'Recommended',
                          value: Sorting.recommended.value),
                      RadioOption<String>(
                          groupValue: _filters[ProductFilterKey.sorting.value],
                          onChanged: (value) => setState(() {
                                _filters[ProductFilterKey.sorting.value] =
                                    value;
                              }),
                          text: 'Price (High to Low)',
                          value: Sorting.priceDesc.value),
                      RadioOption<String>(
                          groupValue: _filters[ProductFilterKey.sorting.value],
                          onChanged: (value) => setState(() {
                                _filters[ProductFilterKey.sorting.value] =
                                    value;
                              }),
                          text: 'Price (Low to High)',
                          value: Sorting.priceAsc.value),
                      const Divider(
                        height: 20,
                        thickness: 1,
                        color: Color(0xFFD9D9D9),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Text(
                            'Price range ${changesCountText(type: ProductFilterKey.priceRanges.value)}',
                            style: GoogleFonts.inter(
                                textStyle: TextStyle(
                                    fontSize: 14,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600))),
                      ),
                      CheckboxOption(
                        text: '\$0 - \$25',
                        value: _filters[ProductFilterKey.priceRanges.value]
                                ?.contains(PriceRange.to25.value) ??
                            false,
                        onChanged: (value) => toggle(
                            ProductFilterKey.priceRanges.value,
                            PriceRange.to25.value),
                      ),
                      CheckboxOption(
                        text: '\$25 - \$50',
                        value: _filters[ProductFilterKey.priceRanges.value]
                                ?.contains(PriceRange.from25to50.value) ??
                            false,
                        onChanged: (value) => toggle(
                            ProductFilterKey.priceRanges.value,
                            PriceRange.from25to50.value),
                      ),
                      CheckboxOption(
                        text: '\$50 - \$100',
                        value: _filters[ProductFilterKey.priceRanges.value]
                                ?.contains(PriceRange.from50to100.value) ??
                            false,
                        onChanged: (value) => toggle(
                            ProductFilterKey.priceRanges.value,
                            PriceRange.from50to100.value),
                      ),
                      CheckboxOption(
                        text: '\$100 - \$200',
                        value: _filters[ProductFilterKey.priceRanges.value]
                                ?.contains(PriceRange.from100to200.value) ??
                            false,
                        onChanged: (value) => toggle(
                            ProductFilterKey.priceRanges.value,
                            PriceRange.from100to200.value),
                      ),
                      CheckboxOption(
                        text: 'Over \$200',
                        value: _filters[ProductFilterKey.priceRanges.value]
                                ?.contains(PriceRange.over200.value) ??
                            false,
                        onChanged: (value) => toggle(
                            ProductFilterKey.priceRanges.value,
                            PriceRange.over200.value),
                      ),
                      const Divider(
                        height: 20,
                        thickness: 1,
                        color: Color(0xFFD9D9D9),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Text(
                            'Gender ${changesCountText(type: ProductFilterKey.genders.value)}',
                            style: GoogleFonts.inter(
                                textStyle: TextStyle(
                                    fontSize: 14,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600))),
                      ),
                      CheckboxOption(
                        text: 'Men',
                        value: _filters[ProductFilterKey.genders.value]
                                ?.contains(Gender.male.value) ??
                            false,
                        onChanged: (value) => toggle(
                            ProductFilterKey.genders.value, Gender.male.value),
                      ),
                      CheckboxOption(
                        text: 'Women',
                        value: _filters[ProductFilterKey.genders.value]
                                ?.contains(Gender.female.value) ??
                            false,
                        onChanged: (value) => toggle(
                            ProductFilterKey.genders.value,
                            Gender.female.value),
                      ),
                      CheckboxOption(
                        text: 'Unisex',
                        value: _filters[ProductFilterKey.genders.value]
                                ?.contains(Gender.unisex.value) ??
                            false,
                        onChanged: (value) => toggle(
                            ProductFilterKey.genders.value,
                            Gender.unisex.value),
                      ),
                      const Divider(
                        height: 20,
                        thickness: 1,
                        color: Color(0xFFD9D9D9),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Text(
                            'Vendor ${changesCountText(type: ProductFilterKey.brands.value)}',
                            style: GoogleFonts.inter(
                                textStyle: TextStyle(
                                    fontSize: 14,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600))),
                      ),
                      buildBrandList(),
                      const SizedBox(height: 50),
                    ]),
                  ),
                ],
              ),
            ),
          ),
          Wrap(
            direction: Axis.horizontal,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                    left: 5, right: 5, top: 15, bottom: 25),
                child: OutlinedButton(
                  onPressed: reset,
                  style: const ButtonStyle(
                      minimumSize: MaterialStatePropertyAll(Size.zero),
                      padding: MaterialStatePropertyAll(
                          EdgeInsets.symmetric(horizontal: 50, vertical: 15))),
                  child: Text(
                    'Reset ${changesCountText()}',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 18,
                            color: Colors.black,
                            fontWeight: FontWeight.w600)),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                    left: 5, right: 5, top: 15, bottom: 25),
                child: ElevatedButton(
                  onPressed: () {
                    widget.onClose(_filters);
                  },
                  style: const ButtonStyle(
                      minimumSize: MaterialStatePropertyAll(Size.zero),
                      padding: MaterialStatePropertyAll(
                          EdgeInsets.symmetric(horizontal: 50, vertical: 15))),
                  child: Text(
                    'Apply',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 18, fontWeight: FontWeight.w600)),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void reset() {
    setState(() {
      _filters = {
        ProductFilterKey.sorting.value: Sorting.mostPopular.value,
      };
    });
  }

  String changesCountText({String? type}) {
    if (type != null) {
      List? list = _filters[type];
      return list == null || list.isEmpty ? '' : '(${list.length})';
    } else {
      num count = 0;
      count += _filters[ProductFilterKey.brands.value] != null
          ? _filters[ProductFilterKey.brands.value].length
          : 0;
      count += _filters[ProductFilterKey.genders.value] != null
          ? _filters[ProductFilterKey.genders.value].length
          : 0;
      count += _filters[ProductFilterKey.priceRanges.value] != null
          ? _filters[ProductFilterKey.priceRanges.value].length
          : 0;
      return count > 0 ? '($count)' : '';
    }
  }

  void toggle(String key, dynamic value) {
    List? list = _filters[key];
    list ??= List.empty(growable: true);
    if (list.contains(value)) {
      list.remove(value);
    } else {
      list.add(value);
    }
    setState(() {
      _filters[key] = list;
    });
  }

  Widget buildBrandList() {
    return FutureBuilder<List<Brand>>(
      future: _fetchBrands(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return const Center(
            child: Text('An error has occurred!'),
          );
        } else if (snapshot.hasData) {
          return Column(
            children: snapshot.data!.map(buildBrandCheckbox).toList(),
          );
        } else {
          return Loading(loading: true);
        }
      },
    );
  }

  Widget buildBrandCheckbox(Brand brand) {
    return CheckboxOption(
      text: brand.name,
      value:
          _filters[ProductFilterKey.brands.value]?.contains(brand.id) ?? false,
      onChanged: (value) => toggle(ProductFilterKey.brands.value, brand.id),
    );
  }
}
