import 'package:shared_preferences/shared_preferences.dart';

import '../shared/gender.dart';

class ProductFilter {

  static Future clear() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(ProductFilterKey.sorting.value);
    await prefs.remove(ProductFilterKey.brands.value);
    await prefs.remove(ProductFilterKey.genders.value);
    await prefs.remove(ProductFilterKey.priceRanges.value);
  }

  static Future<Map<String, dynamic>> get() async {
    final prefs = await SharedPreferences.getInstance();
    Map<String, dynamic> result = {
      ProductFilterKey.sorting.value: prefs.getString(ProductFilterKey.sorting.value) ?? Sorting.mostPopular.value
    };
    var brands = prefs.getStringList(ProductFilterKey.brands.value);
    if (brands != null) {
      result[ProductFilterKey.brands.value] = brands;
    }
    var genders = prefs.getStringList(ProductFilterKey.genders.value);
    if (genders != null) {
      result[ProductFilterKey.genders.value] = genders;
    }
    var priceRanges = prefs.getStringList(ProductFilterKey.priceRanges.value);
    if (priceRanges != null) {
      result[ProductFilterKey.priceRanges.value] = priceRanges;
    }
    return result;
  }

  static Future<int> getCount(Map<String, dynamic> values) async {
    int result = 0;
    if (values[ProductFilterKey.brands.value] != null) {
      result += values[ProductFilterKey.brands.value].length as int;
    }
    if (values[ProductFilterKey.genders.value] != null) {
      result += values[ProductFilterKey.genders.value].length as int;
    }
    if (values[ProductFilterKey.priceRanges.value] != null) {
      result += values[ProductFilterKey.priceRanges.value].length as int;
    }

    return result;
  }

  static Future<void> set(Map<String, dynamic> values) async {
    final prefs = await SharedPreferences.getInstance();
    if (values[ProductFilterKey.brands.value] != null) {
      await prefs.setStringList(ProductFilterKey.brands.value, (values[ProductFilterKey.brands.value] as List).map((item) => item as String).toList());
    } else {
      prefs.remove(ProductFilterKey.brands.value);
    }
    if (values[ProductFilterKey.genders.value] != null) {
      await prefs.setStringList(ProductFilterKey.genders.value, (values[ProductFilterKey.genders.value] as List).map((item) => item as String).toList());
    } else {
      prefs.remove(ProductFilterKey.genders.value);
    }
    if (values[ProductFilterKey.priceRanges.value] != null) {
      await prefs.setStringList(ProductFilterKey.priceRanges.value, (values[ProductFilterKey.priceRanges.value] as List).map((item) => item as String).toList());
    } else {
      prefs.remove(ProductFilterKey.priceRanges.value);
    }
    await prefs.setString(ProductFilterKey.sorting.value, values[ProductFilterKey.sorting.value] ?? Sorting.mostPopular.value);
  }
}

enum Sorting {
  mostPopular('mostPopular'),
  newItems('newItems'),
  recommended('recommended'),
  priceDesc('priceDesc'),
  priceAsc('priceAsc');

  const Sorting(this.value);
  final String value;

  factory Sorting.fromString(String? val) {
    Sorting sorting;

    if (val == Sorting.mostPopular.value) {
      sorting = Sorting.mostPopular;
    } else if (val == Sorting.newItems.value) {
      sorting = Sorting.newItems;
    } else if (val == Sorting.recommended.value) {
      sorting = Sorting.recommended;
    } else if (val == Sorting.priceDesc.value) {
      sorting = Sorting.priceDesc;
    } else {
      sorting = Sorting.priceAsc;
    }

    return sorting;
  }
}

enum PriceRange {
  to25('0_25'),
  from25to50('25_50'),
  from50to100('50_100'),
  from100to200('100_200'),
  over200('200');

  const PriceRange(this.value);
  final String value;

  factory PriceRange.fromString(String val) {
    PriceRange priceRange;

    if (val == PriceRange.to25.value) {
      priceRange = PriceRange.to25;
    } else if (val == PriceRange.from25to50.value) {
      priceRange = PriceRange.from25to50;
    } else  if (val == PriceRange.from50to100.value) {
      priceRange = PriceRange.from50to100;
    } else   if (val == PriceRange.from100to200.value) {
      priceRange = PriceRange.from100to200;
    } else {
      priceRange = PriceRange.over200;
    }

    return priceRange;
  }
}

enum ProductFilterKey {
  brands('brands'),
  genders('genders'),
  priceRanges('priceRanges'),
  sorting('sorting');

  const ProductFilterKey(this.value);
  final String value;
}