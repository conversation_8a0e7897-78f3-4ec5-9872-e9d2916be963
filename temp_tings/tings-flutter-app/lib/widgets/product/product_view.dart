import 'dart:async';
import 'dart:convert';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:tings/widgets/product/check_out.dart';
import 'package:tings/widgets/product/edit_product.dart';
import 'package:tings/widgets/product/product_carousel.dart';
import 'package:tings/widgets/product/purchase_bottom_sheet.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/custom_switch.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/data/wishlist_item.dart';
import 'package:tings/widgets/wishlist/widgets/wishlist_button.dart';
import 'package:tings/widgets/wishlist/widgets/wishlist_details_form.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';
import 'package:transparent_image/transparent_image.dart';

import '../shared/event_tracker.dart';
import '../shared/utils.dart';
import 'data/product.dart';
import 'reverse_purchase_bottom_sheet.dart';

class ProductViewArguments {
  final Product product;
  final WishlistItem? wishlistItem;
  final bool? isOwner;

  ProductViewArguments(
      {required this.product, this.wishlistItem, this.isOwner});
}

class ProductView extends StatefulWidget {
  final Product product;
  final WishlistItem? wishlistItem;
  final bool? isOwner;
  static const String routeName = '/product';

  const ProductView(
      {super.key, required this.product, this.wishlistItem, this.isOwner});

  @override
  State<StatefulWidget> createState() => _ProductViewState();
}

class _ProductViewState extends State<ProductView> {
  int activeIndex = 0;
  final List<Product> _similarProducts = [];
  var _purchased = false;
  var _updated = false;
  Wishlist? _wishlist;

  Timer? _debounce;

  @override
  void initState() {
    EventTracker().track(
        'View Product Details',
        properties: {
          'product_id': widget.product.id,
          'product_title': widget.product.title
        }
    );

    _updatePurchaseStatus();
    _sendViewedToActivity();
    _fetchSimilarProducts();
    if (widget.wishlistItem?.wishListId != null) {
      WishlistDataProvider.getWishlistById(widget.wishlistItem!.wishListId)
          .then((value) => setState((){
        _wishlist = value;
      }));
    }

    super.initState();
  }

  void _updatePurchaseStatus() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      if (widget.wishlistItem?.id != null) {
        bool purchased = await WishlistDataProvider.getPurchaseStatus(
            widget.wishlistItem!.id!);
        setState(() {
          _purchased = purchased;
        });
      } else {
        final response = await Api.get(
            '/api/generic_purchase/by_product/${widget.product.id}');

        if (response.statusCode == 200) {
          setState(() {
            _purchased =
                Api.parse(response.bodyBytes, (res) => res['hasPurchase']);
          });
        } else {
          throw Exception('Failed to load similar products');
        }
      }
    }
  }

  void _sendViewedToActivity() async {
    await Api.post('/api/activity/viewed',
        body: {'product': widget.product.id});
  }

  void _fetchSimilarProducts() async {
    final response =
        await Api.get('/api/similar-products/${widget.product.id}');

    if (response.statusCode == 200) {
      setState(() {
        _similarProducts.addAll(
            Api.parseList<Product>(response.bodyBytes, Product.fromJson));
      });
    } else {
      throw Exception('Failed to load similar products');
    }
  }

  Future _onPurchasedToggleClick(bool purchased) async {
    if (purchased) {
      // this means that we are in friend wishlist item
      if (widget.wishlistItem != null) {
        showPurchaseBottomSheet(context, false, true);
      } else {
        await _purchase(purchased, true);
      }
    } else {
      await _purchase(purchased, true);
    }
  }

  Future _purchase(bool purchased, bool anonymous) async {
    if (widget.wishlistItem != null) {
      if (purchased) {
        setState(() {
          _updated = true;
          _purchased = purchased;
        });
        await Api.post('/api/purchase', body: {
          "wishItem": widget.wishlistItem!.id,
          "anonymous": anonymous
        });
        widget.wishlistItem!.purchased = widget.wishlistItem!.purchased + 1;
      } else {
        showModalBottomSheet<void>(
            context: context,
            isScrollControlled: true,
            barrierColor: Colors.black87,
            builder: (context) {
              return ReversePurchaseBottomSheet(onClose: (result) async {
                if (result) {
                  setState(() {
                    _updated = true;
                    _purchased = purchased;
                  });
                  await Api.post('/api/reverse_purchase',
                      body: {"wishItem": widget.wishlistItem!.id});
                  widget.wishlistItem!.purchased =
                      widget.wishlistItem!.purchased - 1;
                }
                Navigator.of(context).pop(true);
              });
            });
      }
    } else {
      setState(() {
        _updated = true;
        _purchased = purchased;
      });
      if (purchased) {
        await Api.post('/api/generic_purchase',
            body: {"productId": widget.product.id});
      } else {
        await Api.post('/api/reverse_generic_purchase',
            body: {"productId": widget.product.id});
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(_updated),
        ),
        actions: [
          Conditional(
              condition: widget.product.ownProduct == true,
              child: IconButton(
                  onPressed: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => EditProduct(
                              wishlist: _wishlist,
                              product: widget.product,
                            ))).then((value) {
                      if (value == true) {

                      }
                    });
                  },
                  icon:
                  Image.asset('images/icons/edit.png', height: 24, width: 24))
          )
        ],
        // Here we take the value from the MyHomePa
      ),
      bottomNavigationBar: buildBottomBar(),
      body: ListView(
        padding: const EdgeInsets.only(bottom: 30),
        children: <Widget>[
          Stack(
            fit: StackFit.passthrough,
            children: <Widget>[
              CarouselSlider.builder(
                itemCount: widget.product.images.isNotEmpty
                    ? widget.product.images.length
                    : 1,
                options: CarouselOptions(
                  onPageChanged: (index, reason) => setState(() {
                    activeIndex = index;
                  }),
                  height: 378,
                  enlargeCenterPage: true,
                  enlargeStrategy: CenterPageEnlargeStrategy.height,
                  enableInfiniteScroll: false,
                ),
                itemBuilder: (context, index, realIndex) {
                  if (widget.product.images.isNotEmpty) {
                    final urlImage = widget.product.images[index].url;
                    return buildImage(urlImage, index);
                  } else {
                    return Image.asset(
                      "images/product_placeholder.png",
                      width: 328,
                      height: 328,
                    );
                  }
                },
              ),
              Positioned(
                // red box
                right: 58,
                top: 5,
                child: WishlistButton(product: widget.product),
              )
            ],
          ),
          const SizedBox(height: 12),
          buildIndicator(),
          const SizedBox(height: 12),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text(decode(widget.product.title ?? ''),
                  style: const TextStyle(
                      fontSize: 20, fontWeight: FontWeight.w700),
                  textAlign: TextAlign.start)),
          const SizedBox(height: 12),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text('\$${Utils.convertToCurrency(widget.product.price)}',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 20, fontWeight: FontWeight.bold)),
                  textAlign: TextAlign.start)),
          const SizedBox(height: 12),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text(decode(widget.product.description ?? ''),
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(fontSize: 14, height: 1.5)),
                  textAlign: TextAlign.start)),
          const SizedBox(height: 37),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                  widget.product.brand != null
                      ? 'From ${widget.product.brand!.name}'
                      : '',
                  style: const TextStyle(fontSize: 14, height: 1.5),
                  textAlign: TextAlign.start)),
          const SizedBox(height: 42),
          const Divider(thickness: 4, color: Color(0xFFF5F5F5)),
          const SizedBox(height: 32),
          Conditional(
            condition: widget.isOwner == false ||
                widget.isOwner == null && widget.wishlistItem != null,
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Note for family and friends',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w600),
                        textAlign: TextAlign.start),
                    const SizedBox(height: 19),
                    Row(
                      children: [
                        const SizedBox(
                          width: 147,
                          child: Text('Notes:',
                              style: TextStyle(
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.start),
                        ),
                        Text(widget.wishlistItem?.note ?? '',
                            style: const TextStyle(
                                fontSize: 16, fontWeight: FontWeight.w600),
                            textAlign: TextAlign.start),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const SizedBox(
                          width: 147,
                          child: Text('Quantity wanted:',
                              style: TextStyle(
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.start),
                        ),
                        Text(widget.wishlistItem?.quantity.toString() ?? '',
                            style: const TextStyle(
                                fontSize: 16, fontWeight: FontWeight.w600),
                            textAlign: TextAlign.start),
                        Conditional(
                            condition: widget.wishlistItem != null &&
                                widget.wishlistItem!.purchased > 0,
                            child: Padding(
                              padding: const EdgeInsets.only(left: 8),
                              child: Text(
                                  widget.wishlistItem?.purchased ==
                                          widget.wishlistItem?.quantity
                                      ? '(purchased)'
                                      : '(${widget.wishlistItem!.purchased} of ${widget.wishlistItem?.quantity} purchased)',
                                  style: const TextStyle(
                                      fontSize: 16, color: Color(0xFF555555)),
                                  textAlign: TextAlign.start),
                            ))
                      ],
                    ),
                  ],
                )),
          ),
          Conditional(
            condition: widget.isOwner == false ||
                widget.isOwner == null && widget.wishlistItem != null,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: const [
                SizedBox(height: 42),
                Divider(thickness: 4, color: Color(0xFFF5F5F5)),
                SizedBox(height: 32),
              ],
            ),
          ),
          Conditional(
            condition: widget.isOwner == true,
            child: Column(
              children: [
                WishlistDetailsForm(
                  product: widget.product,
                  wishlistItem: widget.wishlistItem,
                  onChange: (note, quantity) {
                    if (_debounce?.isActive ?? false) _debounce?.cancel();
                    _debounce = Timer(const Duration(seconds: 1), () {
                      if (widget.wishlistItem != null) {
                        widget.wishlistItem!.note = note;
                        widget.wishlistItem!.quantity = quantity;
                        WishlistDataProvider.updateItem(widget.wishlistItem!);
                      }
                    });
                  },
                ),
                const SizedBox(height: 38),
                const Divider(thickness: 4, color: Color(0xFFF5F5F5)),
                const SizedBox(height: 32),
              ],
            ),
          ),
          Conditional(
            condition: FirebaseAuth.instance.currentUser != null,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Did you purchase this item?',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 16,
                              color: Colors.black,
                              fontWeight: FontWeight.w600))),
                  const SizedBox(height: 16),
                  Text(
                      'Keep track of your purchased items on your profile under My activity.',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 16, height: 1.5, color: Colors.black))),
                  SizedBox(
                    height: 55,
                    child: InkWell(
                      onTap: () async {
                        await _onPurchasedToggleClick(!_purchased);
                      },
                      child: Row(
                        children: [
                          Text('Mark as purchased',
                              style: GoogleFonts.inter(
                                  textStyle: const TextStyle(
                                      fontSize: 14, color: Colors.black))),
                          const SizedBox(width: 8),
                          SizedBox(
                            width: 38,
                            child: CustomSwitch(
                              value: _purchased,
                              onChanged: (bool value) {
                                if (value) {
                                  EventTracker().track(
                                      'Tap Marked as purchased',
                                      properties: {
                                        'product_id': widget.product.id,
                                        'product_title': widget.product.title
                                      }
                                  );
                                }
                                _onPurchasedToggleClick(value);
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Conditional(
            condition: _similarProducts.isNotEmpty,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  const SizedBox(height: 10),
                  const Divider(thickness: 4, color: Color(0xFFF5F5F5)),
                  const SizedBox(height: 32),
                  Text('Similar gifts you may like',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 16,
                              color: Colors.black,
                              fontWeight: FontWeight.w600))),
                  const SizedBox(height: 16),
                  ProductsCarousel(products: _similarProducts),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String decode(String text) {
    try {
      return utf8.decode(text.codeUnits);
    } catch (e) {
      return text;
    }
  }

  Widget buildImage(String urlImage, int index) => Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        clipBehavior: Clip.hardEdge,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
        child: FadeInImage.memoryNetwork(
          placeholder: kTransparentImage,
          image: urlImage,
          fit: BoxFit.cover,
        ),
      );

  Widget buildBottomBar() => Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: SizedBox(
        height: 100,
        child: Center(
          child: ElevatedButton(
            onPressed: () async {
              EventTracker().track(
                  'Tap Shop it!',
                  properties: {
                    'product_id': widget.product.id,
                    'product_title': widget.product.title
                  }
              );

              final url = widget.product.checkoutUrl;
              Wishlist? wishlist;
              if (widget.wishlistItem != null) {
                wishlist = await WishlistDataProvider.getWishlistById(
                    widget.wishlistItem!.wishListId);
              }

              Navigator.of(context)
                  .push(MaterialPageRoute(
                      builder: (context) => CheckOut(
                            url: url,
                            address: wishlist?.deliveryAddress,
                          )))
                  .then((value) => {
                        if (widget.wishlistItem != null)
                          {
                            showPurchaseBottomSheet(
                                context,
                                true,
                                widget.isOwner != true &&
                                    widget.wishlistItem != null)
                          }
                      });
            },
            child: const Text('Shop It!'),
          ),
        ),
      ));

  void showPurchaseBottomSheet(
      BuildContext context, bool showPurchaseDialog, bool showAnonymousDialog) {
    showModalBottomSheet<void>(
        context: context,
        isScrollControlled: true,
        barrierColor: Colors.black87,
        builder: (context) {
          return PurchaseBottomSheet(
              showAnonymousModal: showAnonymousDialog,
              showPurchaseModal: showPurchaseDialog,
              onClose: (purchased, anonymous) async {
                if (purchased) {
                  EventTracker().track(
                      'Tap Marked as purchased',
                      properties: {
                        'product_id': widget.product.id,
                        'product_title': widget.product.title
                      }
                  );
                  await _purchase(
                      showPurchaseDialog ? purchased : true, anonymous);
                }
                Navigator.of(context).pop(true);
              });
        });
  }

  Widget buildIndicator() => Center(
        child: AnimatedSmoothIndicator(
          activeIndex: activeIndex,
          count: widget.product.images.length,
          effect: const WormEffect(
              dotColor: Color(0xFF969696),
              activeDotColor: Color(0xFF575757),
              dotHeight: 7,
              dotWidth: 7),
        ),
      );
}
