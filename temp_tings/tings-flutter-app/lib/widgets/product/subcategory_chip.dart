import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/search/data/category.dart';


class SubCategoryChip extends StatelessWidget {
  final SubCategory subCategory;
  final bool active;

  const SubCategoryChip({super.key, required this.subCategory, required this.active});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      clipBehavior: Clip.hardEdge,
      padding: const EdgeInsets.only(left: 16, top: 6, bottom: 8, right: 16),
      decoration: BoxDecoration(
        color: active ? Colors.black : Colors.white,
        borderRadius: const BorderRadius.all(Radius.circular(90)),
        border: Border.all(color: Colors.black, width: 1),
      ),
      child: Text(subCategory.name, style: GoogleFonts.inter(textStyle: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: active ? Colors.white : Colors.black)), textAlign: TextAlign.left),
    );
  }
}
