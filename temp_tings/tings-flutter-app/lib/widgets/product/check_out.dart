import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/wishlist/data/delivery_address.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CheckOut extends StatefulWidget {
  final String url;
  final DeliveryAddress? address;

  const CheckOut({super.key, required this.url, this.address});

  @override
  State<CheckOut> createState() => _CheckOutState();
}

class _CheckOutState extends State<CheckOut> {
  var loadingPercentage = 0;
  late final WebViewController controller;

  @override
  void initState() {
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(NavigationDelegate(
        onPageStarted: (url) {
          setState(() {
            loadingPercentage = 0;
          });
        },
        onProgress: (progress) {
          setState(() {
            loadingPercentage = progress;
          });
        },
        onPageFinished: (url) {
          setState(() {
            loadingPercentage = 100;
          });
        },
      ))
      ..loadRequest(
        Uri.parse(widget.url),
      );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          Conditional(
              condition: widget.address != null,
              child: TextButton(
                onPressed: showAddressDialog,
                child: Text(
                  'Delivery address',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                          fontWeight: FontWeight.w700)),
                  textAlign: TextAlign.left,
                ),
              )),
        ],
      ),
      body: Stack(children: [
        WebViewWidget(
          controller: controller,
        ),
      ]),
    );
  }

  void showAddressDialog() {
    showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
              title: Text(
                'Delivery address',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 16, fontWeight: FontWeight.w700)),
                textAlign: TextAlign.left,
              ),
              content: SizedBox(
                height: double.parse(['street', 'unit', 'city', 'state', 'zip']
                    .fold('0', (value, element) {
                  var val = double.parse(value);
                  if (addressHasValue(element)) {
                    val += 70;
                  }
                  return val.toString();
                })),
                child: Column(
                  children: [
                    buildAddressField('street'),
                    buildAddressField('unit'),
                    buildAddressField('city'),
                    buildAddressField('state'),
                    buildAddressField('zip'),
                  ],
                ),
              ),
            ));
  }

  bool addressHasValue(String field) {
    if (widget.address != null) {
      var address = widget.address!.toJson();
      var value = address[field];
      return value.toString().isNotEmpty;
    }
    return false;
  }

  Widget buildAddressField(String field) {
    if (addressHasValue(field)) {
      var address = widget.address!.toJson();
      var value = address[field];
      return ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 0),
        dense: true,
        title: Text(
          capitalize(field),
          style: GoogleFonts.inter(
              textStyle:
                  const TextStyle(fontSize: 16, fontWeight: FontWeight.w700)),
          textAlign: TextAlign.left,
        ),
        subtitle: Text(
          value ?? '',
          style: GoogleFonts.inter(textStyle: const TextStyle(fontSize: 12)),
          textAlign: TextAlign.left,
        ),
        trailing: IconButton(
          icon: const Icon(Icons.copy, size: 18, color: Colors.black87),
          onPressed: () => copyAddressField(field),
        ),
      );
    }
    return const SizedBox();
  }

  void copyAddressField(String field) {
    if (widget.address != null) {
      var address = widget.address!.toJson();
      var value = address[field];
      if (value.toString().isNotEmpty) {
        Clipboard.setData(ClipboardData(text: value));
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${capitalize(field)} copied')),
        );
      }
    }
    Navigator.of(context).pop();
  }

  String capitalize(String s) => s[0].toUpperCase() + s.substring(1);
}
