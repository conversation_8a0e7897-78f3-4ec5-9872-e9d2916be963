
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/product/parsed_product_form.dart';

import '../shared/event_tracker.dart';
import '../wishlist/data/wishlist.dart';
import 'data/parsed_product.dart';
import 'data/product.dart';

class EditProduct extends StatelessWidget {
  final Product product;
  final Wishlist? wishlist;

  EditProduct(
      {super.key, required this.product, this.wishlist}) {
    EventTracker().track('View Edit Products');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Edit product',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
      ),
      body: ParsedProductForm(
        checkoutUrl: product.checkoutUrl,
        wishlist: wishlist,
        hideWishlist: true,
        showInfo: false,
        onSubmit: () {
          Navigator.of(context).pop();
          Navigator.of(context).pop(true);
        },
        product: ParsedProduct(
          title: product.title,
          price: product.price,
          images: product.images.map((e) => e.url).toList(),
          id: product.id,
          notes: product.description,
        ),
      ),
    );
  }

}