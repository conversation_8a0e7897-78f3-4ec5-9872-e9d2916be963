import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';

import '../shared/radio_option.dart';

class PurchaseBottomSheet extends StatefulWidget {
  final Function(bool, bool) onClose;
  final bool showAnonymousModal;
  final bool showPurchaseModal;
  const PurchaseBottomSheet(
      {super.key,
      required this.onClose,
      required this.showPurchaseModal,
      required this.showAnonymousModal});

  @override
  State<StatefulWidget> createState() => _PurchaseBottomSheetState();
}

class _PurchaseBottomSheetState extends State<PurchaseBottomSheet> {
  bool _purchase = false;
  double? _height = null;
  bool _anonymous = false;
  List<Widget> _children = [];

  @override
  void initState() {
    setState(() {
      _children = widget.showPurchaseModal == true
          ? purchaseDialogFields()
          : widget.showAnonymousModal
              ? anonymousDialogFields()
              : [];
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheetContainer(
      title: '',
      onClose: (val) {
        if (val != false) {
          widget.onClose(_purchase, _anonymous);
        } else {
          Navigator.of(context).pop();
        }
      },
      children: _children,
    );
  }

  List<Widget> anonymousDialogFields() {
    return [
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32),
        child: Text('Awesome! How would you like to send the gift?',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.w700,
                    decoration: TextDecoration.none)),
            textAlign: TextAlign.left),
      ),
      const SizedBox(height: 24),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32),
        child: RadioOption<bool>(
            groupValue: _anonymous,
            onChanged: (value) => setState(() {
                  _anonymous = value!;
                  _children = anonymousDialogFields();
                }),
            text: 'Include your name on the gift',
            value: false),
      ),
      const SizedBox(height: 8),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32),
        child: RadioOption<bool>(
            groupValue: _anonymous,
            onChanged: (value) => setState(() {
                  _anonymous = value!;
                  _children = anonymousDialogFields();
                }),
            text: 'Make gift anonymous',
            value: true),
      ),
      const SizedBox(height: 40),
      Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: ElevatedButton(
            onPressed: () async {
              if (widget.showPurchaseModal == false) {
                widget.onClose(true, _anonymous);
              } else {
                widget.onClose(_purchase, _anonymous);
              }
            },
            child: const Text('Continue'),
          ))
    ];
  }

  List<Widget> purchaseDialogFields() {
    return [
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32),
        child: Text('Did you purchase the item?',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.w700,
                    decoration: TextDecoration.none)),
            textAlign: TextAlign.left),
      ),
      const SizedBox(height: 24),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32),
        child: Text(
            'Tell us so we can mark the item as purchased on the wishlist/registry!',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                    decoration: TextDecoration.none)),
            textAlign: TextAlign.left),
      ),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
        child: Text('Don\'t worry, we can keep it anonymous if you want.',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                    decoration: TextDecoration.none)),
            textAlign: TextAlign.left),
      ),
      Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              OutlinedButton(
                onPressed: () {
                  widget.onClose(false, false);
                },
                style: const ButtonStyle(
                    minimumSize: MaterialStatePropertyAll(Size.zero),
                    padding: MaterialStatePropertyAll(
                        EdgeInsets.symmetric(horizontal: 60, vertical: 15))),
                child: Text(
                  'No',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w600)),
                ),
              ),
              const SizedBox(
                width: 16,
              ),
              OutlinedButton(
                onPressed: () {
                  if (widget.showAnonymousModal) {
                    setState(() {
                      _purchase = true;
                      _children = anonymousDialogFields();
                    });
                  } else {
                    widget.onClose(true, false);
                  }
                },
                style: const ButtonStyle(
                    minimumSize: MaterialStatePropertyAll(Size.zero),
                    padding: MaterialStatePropertyAll(
                        EdgeInsets.symmetric(horizontal: 60, vertical: 15))),
                child: Text(
                  'Yes',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w600)),
                ),
              ),
            ],
          )),
    ];
  }
}
