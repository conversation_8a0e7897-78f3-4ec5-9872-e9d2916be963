import '../../../globals.dart';
import '../../brand/brand.dart';
import '../../shared/gender.dart';

class Product {
  final String id;
  final String title;
  final double price;
  final Gender? gender;
  final String? description;
  final Brand? brand;
  final String checkoutUrl;
  final List<ProductImage> images;
  late bool? inWishList;
  late bool? ownProduct;
  ProductImage? thumbnail;
  String? label;

  Product({
    required this.id,
    required this.title,
    required this.price,
    this.gender,
    this.description,
    required this.images,
    this.brand,
    required this.checkoutUrl,
    this.inWishList,
    this.ownProduct,
    this.label,
  }) {
    if (images.isNotEmpty) {
      thumbnail = images[0];
    }
  }

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      title: json['title'],
      price: json['price'],
      gender: json['gender'] != null ? Gender.fromString(json['gender']) : null,
      description: json['description'],
      checkoutUrl: json['checkoutUrl'],
      inWishList: json['inWishList'],
      ownProduct: json['ownProduct'],
      brand: json['brand'] != null ? Brand.fromJson(json['brand']) : null,
      images: json['productImages'] != null
          ? List<ProductImage>.from(
              (json['productImages'] as List<dynamic>).map((e) {
              e['url'] = '$cdnUrl/image/${e['url']}';
              return ProductImage.fromJson(e);
            })).toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'price': price,
      'gender': gender != null ? gender!.value : null,
      'description': description,
      'checkoutUrl': checkoutUrl,
      'brand': brand != null ? brand!.toJson() : null,
      'images': images.map((e) => e.toJson()),
    };
  }
}

class ProductImage {
  final String url;
  final int? width;
  final int? height;
  final int orderBy;

  ProductImage(
      {required this.url, this.width, this.height, required this.orderBy});

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'width': width,
      'height': height,
      'orderBy': orderBy,
    };
  }

  factory ProductImage.fromJson(Map<String, dynamic> json) {
    return ProductImage(
      url: json['url'],
      height: json['height'],
      width: json['width'],
      orderBy: json['orderBy'],
    );
  }
}
