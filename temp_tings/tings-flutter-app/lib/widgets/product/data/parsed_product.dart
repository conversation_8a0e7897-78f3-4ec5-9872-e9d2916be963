class ParsedProduct {
  final String? title;
  final double? price;
  final String? size;
  final String? notes;
  final String? color;
  final String? id;
  final List<String>? images;

  ParsedProduct({
    this.title,
    this.price,
    this.size,
    this.notes,
    this.color,
    this.images,
    this.id,
  });

  factory ParsedProduct.fromJson(Map<String, dynamic> json) {
    return ParsedProduct(
      title: json['title'],
      price: json['price'] + .00,
      color: json['color'],
      size: json['size'],
      notes: json['notes'],
      id: json['id'],
      images: json['images'] != null
          ? List<String>.from(json['images'] as List<dynamic>).toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'price': price,
      'color': color,
      'id': id,
      'size': size,
      'notes': notes,
      'images': images,
    };
  }
}
