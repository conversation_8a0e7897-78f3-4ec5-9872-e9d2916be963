import 'package:flutter/material.dart';
import 'package:transparent_image/transparent_image.dart';

import '../shared/conditional.dart';
import 'data/product.dart';
import 'product_view.dart';

class ProductsCarousel extends StatelessWidget {
  final List<Product> products;
  final double? size;

  const ProductsCarousel({super.key, required this.products, this.size});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: size ?? 120,
      child: Conditional(
          condition: !products.isEmpty,
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 16),
            shrinkWrap: true,
            itemCount: products.length,
            scrollDirection: Axis.horizontal,
            itemBuilder: (context, index) {
              return buildImage(products[index], index, context);
            },
          )),
    );
  }

  Widget buildImage(Product product, int index, BuildContext context) =>
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6),
        child: GestureDetector(
          onTap: () {
            Navigator.pushNamed(
              context,
              ProductView.routeName,
              arguments: ProductViewArguments(product: product),
            );
          },
          child: Container(
            height: size ?? 120,
            width: size ?? 120,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(4)),
            ),
            clipBehavior: Clip.hardEdge,
            child: FadeInImage.memoryNetwork(
              placeholder: kTransparentImage,
              imageErrorBuilder: (context, error, stackTrace) => Image.asset(
                "images/product_placeholder.png",
                width: 328,
                height: 328,
              ),
              image: product.thumbnail?.url ?? '',
              fit: BoxFit.cover,
              height: size ?? 120,
              width: size ?? 120,
            ),
          ),
        ),
      );
}
