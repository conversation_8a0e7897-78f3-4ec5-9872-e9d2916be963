import 'package:flutter/material.dart';
import 'package:tings/widgets/product/subcategory_chip.dart';
import 'package:tings/widgets/search/data/category.dart';

import '../shared/conditional.dart';

class SubCategoriesCarousel extends StatefulWidget {
  final List<SubCategory> subCategories;
  final List<String>? selected;
  final Function(String) toggle;
  const SubCategoriesCarousel(
      {super.key,
      required this.subCategories,
      required this.toggle,
      this.selected});

  @override
  State<StatefulWidget> createState() => _SubCategoriesCarouselState();
}

class _SubCategoriesCarouselState extends State<SubCategoriesCarousel> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.subCategories.isNotEmpty ? 81 : 16,
      child: Conditional(
        condition: widget.subCategories.isNotEmpty,
        child: Padding(
          padding:
              const EdgeInsets.only(top: 8, bottom: 16, right: 16, left: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: widget.subCategories.length,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (context, index) {
                    return buildChip(widget.subCategories[index]);
                  },
                ),
              ),
              const SizedBox(
                height: 8,
              ),
              const Divider(color: Color(0xFFCDCDCD), thickness: 1),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildChip(SubCategory category) => GestureDetector(
        onTap: () {
          widget.toggle(category.id);
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: SubCategoryChip(
              subCategory: category,
              active: widget.selected?.contains(category.id) ?? false),
        ),
      );
}
