import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';

class ReversePurchaseBottomSheet extends StatelessWidget {
  final Function(bool) onClose;
  const ReversePurchaseBottomSheet({super.key, required this.onClose});

  @override
  Widget build(BuildContext context) {
    return BottomSheetContainer(
      title: '',
      onClose: (val) {
        onClose(val);
      },
      height: 250,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32),
          child: Text(
              'Are you sure you wish to mark this as not purchased? This will allow others to purchase this gift',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 18,
                      color: Colors.black,
                      decoration: TextDecoration.none)),
              textAlign: TextAlign.left),
        ),
        const SizedBox(height: 24),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 30),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton(
                  onPressed: () {
                    onClose(false);
                  },
                  style: const ButtonStyle(
                      minimumSize: MaterialStatePropertyAll(Size.zero),
                      padding: MaterialStatePropertyAll(
                          EdgeInsets.symmetric(horizontal: 60, vertical: 15))),
                  child: Text(
                    'No',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 18,
                            color: Colors.black,
                            fontWeight: FontWeight.w600)),
                  ),
                ),
                const SizedBox(
                  width: 16,
                ),
                OutlinedButton(
                  onPressed: () {
                    onClose(true);
                  },
                  style: const ButtonStyle(
                      minimumSize: MaterialStatePropertyAll(Size.zero),
                      padding: MaterialStatePropertyAll(
                          EdgeInsets.symmetric(horizontal: 60, vertical: 15))),
                  child: Text(
                    'Yes',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 18,
                            color: Colors.black,
                            fontWeight: FontWeight.w600)),
                  ),
                ),
              ],
            )),
      ],
    );
  }
}
