import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:ensure_visible_when_focused/ensure_visible_when_focused.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tings/widgets/product/data/parsed_product.dart';
import 'package:tings/widgets/product/parsed_product_info.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/loading.dart';
import 'package:tings/widgets/shared/quantity_selector.dart';
import 'package:tings/widgets/shared/text_input.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';
import 'package:tings/widgets/wishlist/wishlist_items_list.dart';

import '../../globals.dart';

class ParsedProductForm extends StatefulWidget {
  final ParsedProduct? product;
  final String checkoutUrl;
  final Wishlist? wishlist;
  final Function? onSubmit;
  final bool showInfo;
  final bool? hideWishlist;

  const ParsedProductForm(
      {super.key, this.product, this.hideWishlist, required this.checkoutUrl, this.onSubmit, this.wishlist, required this.showInfo});

  @override
  State<ParsedProductForm> createState() => _ParsedProductFormState();
}

class _ParsedProductFormState extends State<ParsedProductForm> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _controller = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  int _quantity = 1;
  List<Wishlist> _wishlists = [];
  List<String> _images = [];
  Wishlist? _selectedWishlist;
  String imageUrl = '';
  bool showSaveButton = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    init();
    super.initState();
  }

  void init() async {
    _focusNode.addListener(focusListener);
    setState(() {
      _controller.text = widget.product?.notes ?? '';
      _titleController.text = widget.product?.title ?? '';
      _priceController.text = widget.product?.price!.toStringAsFixed(2) ?? '';
      _selectedWishlist = widget.wishlist;
      if (widget.product != null &&
          widget.product!.images != null &&
          widget.product!.images!.isNotEmpty) {
        _images = widget.product!.images!;
        if (_images.isNotEmpty) {
          imageUrl = _images.first;
        }
      }
    });
    getWishlists();
    if (widget.showInfo) {
      var prefs = await SharedPreferences.getInstance();
      var hideParsedProductInfo = prefs.getBool('hideParsedProductInfo');
      if (hideParsedProductInfo != true) {
        showInfoDialog();
      }
    }
  }

  void showInfoDialog(){
    showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
            scrollable: true,
            content: ParsedProductInfo(onClose: (hide) async {
              if (hide) {
                var prefs = await SharedPreferences.getInstance();
                prefs.setBool('hideParsedProductInfo', true);
              }
              Navigator.of(context).pop();
            }, showCheckbox: true,)
        )
    );
  }

  Future getWishlists() async {
    var wishlists = await WishlistDataProvider.getWishlistList();
    setState(() {
      _wishlists = wishlists;
      if (_wishlists.isNotEmpty && _selectedWishlist == null) {
        _selectedWishlist = _wishlists[0];
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _priceController.dispose();
    _titleController.dispose();
    _focusNode.removeListener(focusListener);
    super.dispose();
  }

  void onSubmit() {
    if (_formKey.currentState!.validate()) {
      if (widget.product?.id != null) {
        Api.put('/api/user_product/${widget.product!.id!}', body: {
          "title": _titleController.text,
          "price": _priceController.text,
          "productImages": [
            {"url": imageUrl, "orderBy": 1, "width": null, "height": null}
          ],
          "checkoutUrl": widget.checkoutUrl,
          "wishListId": _selectedWishlist?.id,
          "note": _controller.text
        }).then((response) {
          if (response.statusCode == 200) {
            if (widget.onSubmit != null) {
              widget.onSubmit!();
            }
          } else {
            throw Exception('Failed to save product');
          }
        });
      } else {
        Api.post('/api/user_product', body: {
          "title": _titleController.text,
          "price": _priceController.text,
          "productImages": [
            {"url": imageUrl, "orderBy": 1, "width": null, "height": null}
          ],
          "checkoutUrl": widget.checkoutUrl,
          "wishListId": _selectedWishlist?.id,
          "note": _controller.text
        }).then((response) {
          if (response.statusCode == 200) {
            // todo replace this to parent
            if (_selectedWishlist != null) {
              Navigator.popAndPushNamed(
                context,
                WishlistItemsList.routeName,
                arguments: WishlistItemsListArguments(_selectedWishlist!, true),
              );
            }
            if (widget.onSubmit != null) {
              widget.onSubmit!();
            }
          } else {
            throw Exception('Failed to save product');
          }
        });
      }
    }
  }

  void focusListener() {
    setState(() {
      showSaveButton = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      color: Colors.white,
      child: Form(
        key: _formKey,
        child: ListView(
          children: [
            TextInput(
              controller: _titleController,
              title: 'Title',
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: imageUrl.contains('http') ? imageUrl : '$cdnUrl/image/$imageUrl',
                      width: 120,
                      errorWidget: (context, url, error) => Icon(Icons.error),
                    ),
                    Positioned(
                      // red box
                      left: 8,
                      bottom: 8,
                      child: GestureDetector(
                        onTap: showImageSelector,
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                          color: Colors.black,
                          child: Text(
                            'Edit',
                            style: GoogleFonts.inter(
                                textStyle: const TextStyle(
                                    fontSize: 13,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600)),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                const SizedBox(width: 24),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    QuantitySelector(
                      value: _quantity,
                      title: 'Quantity',
                      onChange: (value) {
                        if (value > 0) {
                          setState(() {
                            _quantity = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    Conditional(
                        condition: widget.hideWishlist != true,
                        child: DropdownButton<String>(
                          hint: Text(
                            'Wishlist / Registry',
                            style: GoogleFonts.inter(
                                textStyle: const TextStyle(
                                    fontSize: 18,
                                    color: Colors.black,
                                    decoration: TextDecoration.underline)),
                            textAlign: TextAlign.left,
                          ),
                          underline: Container(
                            height: 1,
                            color: Colors.black,
                          ),
                          items: _wishlists.map((Wishlist value) {
                            return DropdownMenuItem<String>(
                              value: value.id,
                              child: Text(value.name),
                            );
                          }).toList(),
                          value: _selectedWishlist?.id,
                          onChanged: (String? value) {
                            setState(() => _selectedWishlist = _wishlists
                                .firstWhere((element) => element.id == value));
                          },
                        ),
                    )
                  ],
                )
              ],
            ),
            const SizedBox(height: 24),
            TextInput(
              controller: _priceController,
              title: 'Price',
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  RegExp regExp = RegExp(r'[^0-9\.]');
                  if (regExp.hasMatch(value)) {
                    return 'Should be numeric';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            TextInput(
              focusNode: _focusNode,
              controller: _controller,
              title: 'Note for family and friends',
              placeholder: 'e.g. size, color, etc',
            ),
            EnsureVisibleWhenFocused(
              focusNode: _focusNode,
              child: Conditional(
                  condition: showSaveButton,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          FocusManager.instance.primaryFocus?.unfocus();
                        },
                        child: const Text('Save',
                            style: TextStyle(
                                fontSize: 18,
                                color: Colors.white,
                                fontWeight: FontWeight.bold)),
                      ),
                    ],
                  )),
            ),
            const SizedBox(
              height: 100,
            ),
            ElevatedButton(
              onPressed: onSubmit,
              child: Text(widget.product?.id == null ? 'Add to Tings' : 'Submit'),
            ),
            const SizedBox(height: 50),
          ],
        ),
      ),
    );
  }

  String getCurrentImage() => imageUrl;

  void showImageSelector() {
    showModalBottomSheet<String?>(
      context: context,
      isScrollControlled: true,
      barrierColor: Colors.black87,
      builder: (context) {
        return ImagesBottomSheet(
          value: imageUrl,
          images: _images,
        );
      },
    ).then((value) {
      if (value != null) {
        setState(() {
          imageUrl = value;
          if (!_images.contains(value)) {
            _images.insert(0, value);
          }
        });
      }
    });
  }
}

class ImagesBottomSheet extends StatefulWidget {
  final String value;
  final List<String> images;

  const ImagesBottomSheet(
      {super.key, required this.value, required this.images});

  @override
  State<ImagesBottomSheet> createState() => _ImagesBottomSheetState();
}

class _ImagesBottomSheetState extends State<ImagesBottomSheet> {
  final ScrollController _scrollController = ScrollController();
  late String current;
  bool uploading = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    current = widget.value;
  }

  void _pickImage() async {
    try {

        if (uploading != true) {
        final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
        if (image != null) {
          setState(() {
            uploading = true;
          });
          var uri = Uri(
            scheme: apiScheme,
            host: apiHost,
            port: apiPort,
            path: '/api/upload/PRODUCT',
          );
          var request = MultipartRequest("POST", uri);
          request.files.add(await MultipartFile.fromPath(
            'file',
            image.path,
          ));
          var response = await request.send();
          if (response.statusCode == 200) {
            response.stream.transform(utf8.decoder).listen((value) async {
              var src = jsonDecode(value)['path'];
              setState(() {
                current = src;
              });
              Navigator.of(context).pop(src);
            });
          }
          setState(() {
            uploading = false;
          });
        }
      }
    } on Exception catch(e) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please allow access to your photos in settings for the Tings app.', style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14, color: Colors.black))),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheetContainer(
      title: 'Select image',
      closeIcon: Image.asset(
        "images/icons/checkbox_filled.png",
        width: 32,
        height: 32,
      ),
      onClose: (val) {
        Navigator.of(context).pop(current == widget.value ? null : current);
      },
      height: 500,
      children: [
        Conditional(
          condition: !uploading,
          alternate: const Expanded(child: Loading(loading: true)),
          child: Expanded(
            child: MasonryGridView.count(
                controller: _scrollController,
                itemCount: widget.images.length,
                padding: const EdgeInsets.only(bottom: 50, left: 16, right: 16),
                // the number of columns
                crossAxisCount: 4,
                // vertical gap between two items
                mainAxisSpacing: 10,
                // horizontal gap between two items
                crossAxisSpacing: 10,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        current = widget.images[index];
                      });
                    },
                    child: Container(
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        border: widget.images[index] == current
                            ? Border.all(color: Color(0xFFFFC107), width: 3)
                            : null,
                      ),
                      child: CachedNetworkImage(
                        imageUrl: widget.images[index].contains('http') ? widget.images[index] : '$cdnUrl/image/${widget.images[index]}',
                        errorWidget: (context, url, error) => Icon(Icons.error),
                      ),
                    ),
                  );
                }),
          ),
        ),
        Conditional(
          condition: !uploading,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: ElevatedButton(
              onPressed: (){_pickImage();},
              style: const ButtonStyle(
                  shape: MaterialStatePropertyAll(RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.zero),
                  )),
                  backgroundColor: MaterialStatePropertyAll(Color(0xff5b5b5b)),
                  minimumSize: MaterialStatePropertyAll(Size.fromHeight(54)),
                  textStyle: MaterialStatePropertyAll(
                      TextStyle(fontSize: 18, fontWeight: FontWeight.w600))),
              child: const Text('Add Photo From Camera Roll'),
            ),
          ),
        ),
      ],
    );
  }
}
