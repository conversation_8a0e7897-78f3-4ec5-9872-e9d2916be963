import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/product/parse_product.dart';
import 'package:tings/widgets/shared/text_input.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';

import '../shared/event_tracker.dart';

class ParseProductUrlForm extends StatefulWidget {
  final Wishlist? wishlist;
  const ParseProductUrlForm({super.key, this.wishlist});
  static const String routeName = '/parse-product-test';

  @override
  State<ParseProductUrlForm> createState() => _ParseProductUrlFormState();
}

class _ParseProductUrlFormState extends State<ParseProductUrlForm> {
  final TextEditingController _controller = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    EventTracker().track('New Item to Tings');
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future submit() async {
    if (_formKey.currentState!.validate()) {
      Navigator.pushNamed(
        context,
        ParseProduct.routeName,
        arguments: ParseProductArguments(
            url: _controller.text, wishlist: widget.wishlist),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Add Items to Tings',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: ListView(
              children: [
                Text(
                  'Paste a URL to add any items from the web directly to your Tings wishlist/registry!',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                          fontWeight: FontWeight.w700)),
                  textAlign: TextAlign.left,
                ),
                const SizedBox(
                  height: 8,
                ),
                TextFormField(
                  controller: _controller,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Field is required';
                    }
                    if (!Uri.parse(value).isAbsolute) {
                      return 'Incorrect URL';
                    }
                    return null;
                  },
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          decoration: TextDecoration.none)),
                  decoration: const InputDecoration(
                    labelText: 'Paste a URL',
                    errorStyle: TextStyle(height: 0),
                  ),
                ),
                const SizedBox(
                  height: 16,
                ),
                ElevatedButton(
                  onPressed: submit,
                  child: const Text('Search'),
                ),
                const SizedBox(
                  height: 40,
                ),
                Text(
                  'Can\'t find your item on Tings?',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w700)),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(
                  height: 8,
                ),
                Text(
                  'We make it easy for you to build your dream wishlist/registry!',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                  )),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(
                  height: 32,
                ),
                Image.asset("images/wishlist_background.png"),
                Center(child: Image.asset("images/btn_mytings.png")),
                const SizedBox(
                  height: 16,
                ),
                /*const SizedBox(
                  height: 56,
                ),
                OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _controller.text =
                          "https://www.crateandbarrel.com/equinox-cream-sweater-knit-throw-blanket-70x50/s641620";
                    });
                  },
                  child: const Text('Place test route'),
                ),*/
              ],
            ),
          ),
        ),
      ),
    );
  }
}
