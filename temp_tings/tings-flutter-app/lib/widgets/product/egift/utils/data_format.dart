// import 'package:encrypt/encrypt.dart';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tings/globals.dart';

String? initialLink;

String urlEncodeData(String data) {
  return Uri.encodeComponent(data);
  // return Uri.encodeComponent(encryptData(data));
}

String urlDecodeData(String data) {
  var res = '';
  try {
    res = Uri.decodeComponent(data);
    // res = decryptData(Uri.decodeComponent(data));
  } catch (e) {
    print(e);
    return '';
  }
  return res;
}

String base64UrlEncodeString(String input) {
  var base64Encoded = base64Encode(utf8.encode(input));
  // Make URL-safe
  return base64Encoded
      .replaceAll('+', '-')
      .replaceAll('/', '_')
      .replaceAll('=', '');
}

String base64UrlDecodeString(String input) {
  // Add removed '=' padding
  var mod = input.length % 4;
  var padding = mod != 0 ? 4 - mod : 0;
  var base64Encoded =
      input.replaceAll('-', '+').replaceAll('_', '/') + '=' * padding;
  return utf8.decode(base64Decode(base64Encoded));
}

Future<bool> shareImageFromAssets({
  required String receiver,
  required String filename,
  required String assetPath,
  required String urlToShare,
}) async {
  try {
    // Load the image from the assets
    final ByteData bytes = await rootBundle.load(assetPath);
    final Uint8List list = bytes.buffer.asUint8List();

    // Create a temporary file from the asset
    final Directory tempDir = await getTemporaryDirectory();
    final File file = await File('${tempDir.path}/$filename').create();
    await file.writeAsBytes(list);

    // Encode the URL and prepare the shared content
    final String sharedUrl = '$shareURL?${Uri.encodeFull(urlToShare)}';
    print(sharedUrl);

    final result = await Share.shareWithResult(
      "$receiver, here's a gift for you!\n\n $sharedUrl",
      subject: 'Share gifts',
    );

    // // Share the file and URL
    // final ShareResult result = await Share.shareXFiles(
    //   [XFile(file.path)],
    //   text: sharedUrl,
    // );

    // Handle the share result
    if (result.status == ShareResultStatus.success) {
      return true;
    } else {
      return false;
    }
  } catch (e) {
    return false;
  }
}


// String encryptData(String plainText, {String keyString = 'tings-flutterapp'}) {
//   final key = Key.fromUtf8(keyString);
//   final iv = IV.fromLength(16);
//   final encrypter = Encrypter(AES(key, mode: AESMode.cbc, padding: 'PKCS7'));

//   final encrypted = encrypter.encrypt(plainText, iv: iv);
//   // Convert to Base64 URL Safe string
//   return base64UrlEncodeString(encrypted.base64);
// }

// String decryptData(String encryptedData, {String keyString = 'tings-flutterapp'}) {
//   final key = Key.fromUtf8(keyString);
//   final iv = IV.fromLength(16);
//   final encrypter = Encrypter(AES(key, mode: AESMode.cbc, padding: 'PKCS7'));

//   try {
//     // Convert from Base64 URL Safe string
//     var base64Decoded = base64UrlDecodeString(encryptedData);
//     return encrypter.decrypt(Encrypted.from64(base64Decoded), iv: iv);

//   } catch (e) {
//     print(e);
//     return '';
//   }
// }



