import 'package:flutter/material.dart';
import 'package:tings/widgets/product/egift/app_text.dart';

class CustomChips extends StatelessWidget {
  final String label;
  final bool isSelected;
  final double? height;
  final Color? focusColor;
  final Function()? onTap;

  const CustomChips({
    Key? key,
    required this.label,
    required this.isSelected,
    this.focusColor,
    this.height,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: 10.0),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
        width: width * 0.2,
        // decoration: BoxDecoration(
        //   borderRadius: BorderRadius.circular(50),
        //   color: isSelected
        //       ? focusColor ?? const Color(0xFF252B5C)
        //       : Colors.grey[100],
        // ),
        decoration: BoxDecoration(
        color: isSelected ? Colors.black : Colors.white,
        borderRadius: const BorderRadius.all(Radius.circular(90)),
        border: Border.all(color: Colors.black, width: 1),
      ),
        child: Center(
          child: AppText.small(
            label,
            color: isSelected ? Colors.white : focusColor ?? const Color(0xFF676B8E),
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
