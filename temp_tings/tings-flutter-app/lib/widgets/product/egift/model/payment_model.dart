import 'package:flutter/material.dart';

class PaymentModel {
  final String iconUrl;
  final String name;
  final String androidUrl;
  final String iosUrl;
  final String deeplink;

  PaymentModel({
    required this.iconUrl,
    required this.name,
    required this.androidUrl,
    required this.iosUrl,
    required this.deeplink,
  });

  @override
  String toString() {
    return '$iconUrl, $name, $androidUrl, $iosUrl, $deeplink';
  }

  static PaymentModel fromString(String str) {
    final parts = str.split(', ');
    if (parts.length != 5) {
      throw const FormatException("Invalid input for PaymentModel");
    }

    final iconURl = parts[0];
    final name = parts[1];
    final androidUrl = parts[2];
    final iosUrl = parts[3];
    final deeplink = parts[4];

    return PaymentModel(
      iconUrl: iconURl,
      name: name,
      androidUrl: androidUrl,
      iosUrl: iosUrl,
      deeplink: deeplink,
    );
  }
}
