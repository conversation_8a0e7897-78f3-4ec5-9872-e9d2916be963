import 'package:flutter/material.dart';
import 'package:gif/gif.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/product/egift/dialog/egift_dialog_preview.dart';
import 'package:tings/widgets/product/egift/egift_preview.dart';
import 'package:tings/widgets/product/egift/utils/data_format.dart';
import 'dart:math' as math;

class EgiftDialog extends StatefulWidget {
  final EgiftPreviewArguments arguments;

  const EgiftDialog({super.key, required this.arguments});

  @override
  State<EgiftDialog> createState() => _EgiftDialogState();
}

class _EgiftDialogState extends State<EgiftDialog>
    with TickerProviderStateMixin {
  bool unwrapped = false;
  late GifController _controller;

  @override
  void initState() {
    super.initState();
    _controller = GifController(vsync: this);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: buildBottomBar(),
      // implement the massonry layout
      body: SafeArea(
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 400), // Animation duration
          child: unwrapped
              ? EgiftDialogPreview(
                  key: const ValueKey(
                      'EgiftDialogPreview'), // Unique key for the first widget
                  arguments: widget.arguments,
                )
              : Gif(
                  key: const ValueKey(
                      'UnwrappingGif'), // Unique key for the second widget
                  image: const AssetImage("images/unwrapping.gif"),
                  controller: _controller,
                  autostart: Autostart.no,
                ),
        ),
      ),
    );
  }

  Widget buildBottomBar() => Container(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (unwrapped && widget.arguments.paymentUrl.isNotEmpty)
            RichText(
              text: TextSpan(
                style: const TextStyle(
                  fontSize: 16.0,
                  color: Colors.black,
                ),
                children: <TextSpan>[
                  const TextSpan(text: 'Redeem your cash on '),
                  TextSpan(
                    text: widget.arguments.paymentUrl,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          SizedBox(
            height: 90,
            child: Center(
              child: ElevatedButton(
                onPressed: () async {
                  if (!unwrapped) {
                    _controller.reset();
                    _controller.forward();

                    await Future.delayed(const Duration(seconds: 6), () {});

                    setState(() {
                      unwrapped = true;
                    });

                    initialLink = '';
                    _controller.stop();
                  } else {
                    Navigator.of(context).pop();
                  }

                  // EventTracker().track(
                  //     'Tap Shop it!',
                  //     properties: {
                  //       'product_id': widget.product.id,
                  //       'product_title': widget.product.title
                  //     }
                  // );
                },
                child: Text(
                  !unwrapped ? 'Unwrap your gift!' : 'Done',
                  style: const TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ));
}
