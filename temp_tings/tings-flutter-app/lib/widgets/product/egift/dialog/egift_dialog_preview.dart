import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/product/egift/egift_preview.dart';
import 'package:tings/widgets/shared/utils.dart';
import 'package:transparent_image/transparent_image.dart';

class EgiftDialogPreview extends StatelessWidget {
  final EgiftPreviewArguments arguments;

  const EgiftDialogPreview({super.key, required this.arguments});

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.only(bottom: 30),
      children: <Widget>[
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CarouselSlider.builder(
              itemCount: 1,
              options: CarouselOptions(
                height: 378,
                enlargeCenterPage: true,
                enlargeStrategy: CenterPageEnlargeStrategy.height,
                enableInfiniteScroll: false,
              ),
              itemBuilder: (context, index, realIndex) {
                if (arguments.imgUrl.isNotEmpty) {
                  final urlImage = arguments.imgUrl;
                  return buildImage(urlImage, index);
                } else {
                  return Image.asset(
                    "images/product_placeholder.png",
                    width: 328,
                    height: 328,
                  );
                }
              },
            ),
            const SizedBox(height: 12),
            if (arguments.paymentUrl.isNotEmpty)
              Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Text(
                      '\$${Utils.convertToCurrency(arguments.cashAmount)}',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 30, fontWeight: FontWeight.bold)),
                      textAlign: TextAlign.start)),
            const SizedBox(height: 12),
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                    "${arguments.receiver}, here's a gift from ${arguments.sender}!",
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold)),
                    textAlign: TextAlign.start)),
            const SizedBox(height: 24),
            Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(arguments.message,
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    )),
                    textAlign: TextAlign.center)),
          ],
        ),
      ],
    );
  }

  Widget buildImage(String urlImage, int index) => Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        clipBehavior: Clip.hardEdge,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
        child: FadeInImage.memoryNetwork(
          placeholder: kTransparentImage,
          image: urlImage,
          fit: BoxFit.cover,
        ),
      );
}
