import 'package:flutter/material.dart';
import 'package:tings/widgets/product/check_out.dart';
import 'package:tings/widgets/product/egift/model/payment_model.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';

class EgiftPaymentDialog extends StatelessWidget {
  final List<PaymentModel> paymentMethods = [
    PaymentModel(
      iconUrl: 'images/app_wallet.png',
      name: 'Apple Pay',
      androidUrl: 'https://www.apple.com/apple-pay/',
      iosUrl: 'https://www.apple.com/apple-pay/',
      deeplink: 'shoebox://',
    ),
    PaymentModel(
      iconUrl: 'images/google_pay.png',
      name: 'Google Pay',
      androidUrl:
          'https://play.google.com/store/apps/details?id=com.google.android.apps.nbu.paisa.user',
      iosUrl: 'https://apps.apple.com/app/id1193357041',
      deeplink: 'gpay://',
    ),
    PaymentModel(
        iconUrl: 'images/venmo_logo.png',
        name: '<PERSON>en<PERSON>',
        androidUrl: 'https://venmo.com/account/sign-in',
        iosUrl: 'https://venmo.com/account/sign-in',
        deeplink: 'venmo://'),
    PaymentModel(
      iconUrl: 'images/cashapp_logo.png',
      name: 'Cash App',
      androidUrl: 'https://cash.app/login',
      iosUrl: 'https://cash.app/login',
      deeplink: 'squarecash://',
    ),
    PaymentModel(
      iconUrl: 'images/starbucks_logo.png',
      name: 'Starbucks',
      androidUrl: 'https://www.starbucks.com/account/signin?ReturnUrl=%2F',
      iosUrl: 'https://www.starbucks.com/account/signin?ReturnUrl=%2F',
      deeplink: 'starbucks://',
    ),
    // PaymentModel(
    //   iconUrl: 'images/zelle_logo.png',
    //   name: 'Zelle',
    //   url: 'https://www.zellepay.com/',
    //   deeplink: 'zelle://',
    // ),
  ];

  EgiftPaymentDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return BottomSheetContainer(
        title: 'SEND VIA',
        onClose: (bool applied) {
          Navigator.of(context).pop('');
        },
        children: [
          ListView.builder(
            shrinkWrap: true,
            itemCount: paymentMethods.length,
            itemBuilder: (BuildContext context, int index) {
              var model = paymentMethods[index];
              return Card(
                elevation: 0,
                color: Colors.transparent,
                child: ListTile(
                  leading: Image.asset(
                    model.iconUrl,
                    width: size.width * 0.1,
                  ),
                  title: Text(model.name),
                  onTap: () async {
                    Navigator.of(context).pop(model.toString());
                    // Navigator.of(context).push(MaterialPageRoute(
                    // builder: (context) => CheckOut(
                    //       url: method['url'],
                    //     )));
                  },
                ),
              );
            },
          )
        ]);
  }
}
