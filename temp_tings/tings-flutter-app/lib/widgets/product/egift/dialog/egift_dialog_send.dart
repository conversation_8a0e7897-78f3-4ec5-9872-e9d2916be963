import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tings/widgets/product/check_out.dart';
import 'package:tings/widgets/product/egift/model/payment_model.dart';
import 'package:tings/widgets/product/egift/utils/data_format.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';

class EgiftDialogSend extends StatefulWidget {
  final String paymentName;
  final String giftURL;
  final String receiver;

  const EgiftDialogSend(
      {super.key,
      required this.receiver,
      required this.paymentName,
      required this.giftURL});

  @override
  State<EgiftDialogSend> createState() => _EgiftDialogSendState();
}

class _EgiftDialogSendState extends State<EgiftDialogSend> {
  @override
  Widget build(BuildContext context) {
    return BottomSheetContainer(
        title: 'SEND GIFT',
        onClose: (bool applied) {
          Navigator.of(context).pop();
        },
        children: [
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                  'Have you completed your payment using ${widget.paymentName}?',
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.normal),
                  textAlign: TextAlign.start)),
          Container(
            height: 100,
            padding: const EdgeInsets.symmetric(horizontal: 32),
            margin: const EdgeInsets.only(top: 15),
            child: Center(
              child: ElevatedButton(
                onPressed: () async {
                  final result = await shareImageFromAssets(
                      receiver: widget.receiver,
                      filename: 'giftbox.png',
                      assetPath: 'images/giftbox.png',
                      urlToShare: widget.giftURL);
                  // final bytes = await rootBundle.load('images/giftbox.png');
                  // final list = bytes.buffer.asUint8List();

                  // final tempDir = await getTemporaryDirectory();
                  // final file =
                  //     await File('${tempDir.path}/giftbox.png').create();
                  // file.writeAsBytesSync(list);

                  // final sharedUrl =
                  //     '${urlEncodeData()}';

                  // final result = await Share.shareXFiles(
                  //   [XFile(file.path)],
                  //   text: sharedUrl,
                  // );

                  if (result && mounted) {
                    Navigator.of(context).pop('Success');
                  }
                },
                child: const Text(
                  'Send Gift',
                  style: TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          )
        ]);
  }
}


 
// //  Widget buildBottomSheet(BuildContext context) {
// //     return BottomSheetContainer(
//       title: 'Filter',
//       onClose: (bool applied) {
//         Navigator.pop(context);
//         onClose(null);
//       },
// //       children: [
// //         ProductFilterForm(onClose: (filters) {
// //           Navigator.pop(context);
// //           onClose(filters);
// //         }, initial: initial,)
// //       ],
// //     );
// //   }