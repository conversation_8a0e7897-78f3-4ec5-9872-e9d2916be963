import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tings/widgets/product/data/product.dart';
import 'package:tings/widgets/product/egift/egift_masonry.dart';
import 'package:tings/widgets/product/parse_product_url_form.dart';
import 'package:tings/widgets/product/product_filter.dart';
import 'package:tings/widgets/product/subcategories_carousel.dart';
import 'package:tings/widgets/search/data/category.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';
import 'package:tings/widgets/shared/conditional.dart';

import '../../shared/event_tracker.dart';
import '../../shared/notifications_button.dart';
import '../../shared/loading.dart';
import '../../wishlist/calendar.dart';
import '../product_filter_form.dart';
import '../products_masonry.dart';
import 'dart:developer';

class EgiftList extends StatefulWidget {
  const EgiftList(
      {super.key,
      this.centerTitle,
      this.title,
      this.category,
      this.search,
      this.showBackButton});
  static const String routeName = '/products';
  final String? category;
  final String? search;
  final bool? showBackButton;
  final bool? centerTitle;
  final Widget? title;

  @override
  State<EgiftList> createState() => _EgiftListState();
}

class _EgiftListState extends State<EgiftList> {
  final ScrollController _controller = ScrollController();
  final product = Product.fromJson({
    'id': '1',
    'title': '',
    'price': 25.0,
    'gender': null,
    'description': '',
    'checkoutUrl': 'https://venmo.com/account/sign-in',
    'brand': null,
    'images': {
      'url':
          'https://imgtr.ee/images/2024/01/11/e85c29a4864e4e6c36d9461cf89a52bf.png',
      'width': 165,
      'height': 242,
      'orderBy': 0,
    },
  });

  final List<Product> _products = <Product>[];
  final List<SubCategory> _subCategories = <SubCategory>[];
  List<String>? _selectedSubCategories;
  int _page = 0;
  int _filtersCount = 0;
  bool _fullListLoaded = false;
  bool _loading = false;
  Map<String, dynamic> _filters = {'sorting': 'mostPopular'};

  @override
  void initState() {
    init();
    EventTracker().track('View Explore');
    super.initState();
  }

  void init() async {
    setState(() {
      _subCategories.clear();
    });
    if (widget.category != null) {
      await _fetchSubcategories();
      setState(() {
        _selectedSubCategories = List.empty(growable: true);
      });
      // _fetchProducts(0);
    } /* else {
      var filters = await ProductFilter.get();
      setState(() {
        _filters = filters;
      });
      _fetchProducts(0);
      ProductFilter.getCount(filters).then((value) => setState(() {
        _filtersCount = value;
      }));
    }*/

    _fetchProducts(0);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  _onScroll() {
    if (_controller.offset >= _controller.position.maxScrollExtent) {
      if (!_fullListLoaded && !_loading) {
        setState(() {
          log("Increment _page ${_page.toString()}");
          _page = _page + 1;
        });
        // _fetchProducts(_page);
      }
    }
  }

  Future _fetchSubcategories() async {
    final response =
        await Api.get('/api/sub_category/category/${widget.category}');

    if (response.statusCode == 200) {
      setState(() {
        _subCategories.addAll(Api.parseList<SubCategory>(
            response.bodyBytes, SubCategory.fromJson));
      });
    } else {
      throw Exception('Failed to load subcategories');
    }
  }

  void _fetchProducts(int page) async {
    if (!_loading) {
      setState(() {
        _loading = true;
      });
      var queryParameters = _filters;
      if (widget.category != null) {
        queryParameters['category'] = widget.category;
        if (_selectedSubCategories?.isNotEmpty ?? false) {
          queryParameters['sub_categories'] = _selectedSubCategories;
        }
      }
      queryParameters['pageSize'] = '10';
      queryParameters['page'] = page.toString();
      final response = await Api.get('/api/product/search',
          queryParameters: queryParameters);

      setState(() {
        _loading = false;
      });
      if (response.statusCode == 200) {
        List<Product> newProducts =
            Api.parseList<Product>(response.bodyBytes, Product.fromJson);
        setState(() {
          _products.addAll(newProducts);
          _fullListLoaded = jsonDecode(response.body)['last'];
        });
      } else {
        throw Exception('Failed to load products');
      }
    }
  }

  void toggleSubCategory(String id) async {
    if (widget.category != null) {
      var selected = _selectedSubCategories;
      if (selected != null) {
        if (selected.contains(id)) {
          selected.remove(id);
        } else {
          selected.add(id);
        }
      } else {
        selected = [id];
      }
      setState(() {
        _products.clear();
        _fullListLoaded = false;
        _page = 0;
        _selectedSubCategories = selected;
      });
      _fetchProducts(_page);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          actions: [
            Conditional(
                condition: widget.category == null,
                child: const NotificationsButton()),
            Conditional(
                condition: widget.category == null,
                child: IconButton(
                    padding: EdgeInsets.only(bottom: 9),
                    onPressed: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const Calendar()));
                    },
                    icon: Image.asset(
                      "images/iconly/Light/Calendar.png",
                      width: 24,
                      height: 24,
                    ))),
          ],
          leading: widget.showBackButton == true
              ? IconButton(
                  icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
                      height: 24, width: 24),
                  onPressed: () => Navigator.of(context).pop(),
                )
              : null,
          actionsIconTheme: const IconThemeData(color: Colors.black87),
          iconTheme: const IconThemeData(opacity: 1, color: Colors.black87),
          centerTitle: widget.centerTitle,
          title: widget.title ??
              Image.asset(
                "images/logo.png",
                width: 66,
                height: 39,
              ),
        ),
        // implement the massonry layout
        body: Column(
          mainAxisAlignment:
              _loading ? MainAxisAlignment.center : MainAxisAlignment.start,
          children: [
            SubCategoriesCarousel(
                subCategories: _subCategories,
                toggle: toggleSubCategory,
                selected: _selectedSubCategories),
            Expanded(
              child: Conditional(
                condition: false,
                alternate: Stack(
                  children: <Widget>[
                    EgiftMasonry(
                        products: [product] + _products,
                        controller: _controller),
                    Loading(loading: _loading),
                  ],
                ),
                child: Center(
                  child: Conditional(
                    condition: _loading,
                    alternate: Text('No products found',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                                fontSize: 20, fontWeight: FontWeight.w700)),
                        textAlign: TextAlign.center),
                    child: Loading(loading: _loading),
                  ),
                ),
              ),
            ),
          ],
        ));
  }
}

class FiltersButton extends StatelessWidget {
  final Function(Map<String, dynamic>?) onClose;
  final Map<String, dynamic> initial;
  final int count;

  const FiltersButton(
      {super.key,
      required this.initial,
      required this.onClose,
      required this.count});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Conditional(
          condition: count > 0,
          child: Positioned(
            right: 1,
            top: 4,
            child: Container(
              width: 18,
              height: 18,
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                color: Color(0xffFFC107),
                border: Border.all(color: Colors.white, width: 2),
                borderRadius: BorderRadius.all(Radius.circular(90)),
              ),
              child: Center(
                child: Text(count.toString(),
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            color: Colors.black,
                            fontSize: 10,
                            fontWeight: FontWeight.w700)),
                    textAlign: TextAlign.center),
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget buildBottomSheet(BuildContext context) {
    return BottomSheetContainer(
      title: 'Filter',
      onClose: (bool applied) {
        Navigator.pop(context);
        onClose(null);
      },
      children: [
        ProductFilterForm(
          onClose: (filters) {
            Navigator.pop(context);
            onClose(filters);
          },
          initial: initial,
        )
      ],
    );
  }
}
