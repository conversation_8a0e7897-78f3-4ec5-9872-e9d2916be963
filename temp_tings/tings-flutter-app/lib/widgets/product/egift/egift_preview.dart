import 'dart:convert';
import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/product/check_out.dart';
import 'package:tings/widgets/product/egift/dialog/egift_dialog_send.dart';
import 'package:tings/widgets/product/egift/dialog/egift_payment_dialog.dart';
import 'package:tings/widgets/product/egift/model/payment_model.dart';
import 'package:tings/widgets/product/egift/sender_confirmation.dart';
import 'package:tings/widgets/product/egift/utils/data_format.dart';
import 'package:tings/widgets/shared/utils.dart';
import 'package:transparent_image/transparent_image.dart';
import 'package:url_launcher/url_launcher.dart';

class EgiftPreviewArguments {
  final String imgUrl;
  final double cashAmount;
  final String sender;
  final String receiver;
  final String message;
  final String paymentUrl;

  EgiftPreviewArguments({
    required this.imgUrl,
    required this.cashAmount,
    required this.sender,
    required this.receiver,
    required this.message,
    required this.paymentUrl,
  });

  factory EgiftPreviewArguments.fromJsonString(String jsonString) {
    Map<String, dynamic> jsonMap = json.decode(jsonString);

    return EgiftPreviewArguments(
      imgUrl: jsonMap['imgUrl'],
      cashAmount: jsonMap['cashAmount'].toDouble(),
      sender: jsonMap['sender'],
      receiver: jsonMap['receiver'],
      message: jsonMap['message'],
      paymentUrl: jsonMap['paymentUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'imgUrl': imgUrl,
      'cashAmount': cashAmount,
      'sender': sender,
      'receiver': receiver,
      'message': message,
      'paymentUrl': paymentUrl,
    };
  }

  String toJsonString() {
    return json.encode(toJson());
  }
}

class EgiftPreview extends StatefulWidget {
  const EgiftPreview({
    super.key,
    required this.arguments,
  });
  static const String routeName = '/egiftpreview';
  final EgiftPreviewArguments arguments;

  @override
  State<EgiftPreview> createState() => _EgiftPreviewState();
}

class _EgiftPreviewState extends State<EgiftPreview> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
                height: 24, width: 24),
            onPressed: () => Navigator.of(context).pop(),
          ),
          actionsIconTheme: const IconThemeData(color: Colors.black87),
          iconTheme: const IconThemeData(opacity: 1, color: Colors.black87),
        ),
        bottomNavigationBar: buildBottomBar(context),
        // implement the massonry layout
        body: ListView(
          padding: const EdgeInsets.only(bottom: 30),
          children: <Widget>[
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                    width: double.infinity,
                    child: Text('${widget.arguments.receiver} will see this:',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                          fontSize: 16,
                        )),
                        textAlign: TextAlign.center)),
                CarouselSlider.builder(
                  itemCount: 1,
                  options: CarouselOptions(
                    height: 378,
                    enlargeCenterPage: true,
                    enlargeStrategy: CenterPageEnlargeStrategy.height,
                    enableInfiniteScroll: false,
                  ),
                  itemBuilder: (context, index, realIndex) {
                    if (widget.arguments.imgUrl.isNotEmpty) {
                      final urlImage = widget.arguments.imgUrl;
                      return buildImage(urlImage, index);
                    } else {
                      return Image.asset(
                        "images/product_placeholder.png",
                        width: 328,
                        height: 328,
                      );
                    }
                  },
                ),
                const SizedBox(height: 12),
                if (widget.arguments.cashAmount > 0)
                  Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Text(
                          '\$${Utils.convertToCurrency(widget.arguments.cashAmount)} USD',
                          style: GoogleFonts.inter(
                              textStyle: const TextStyle(
                                  fontSize: 30, fontWeight: FontWeight.bold)),
                          textAlign: TextAlign.start)),
                const SizedBox(height: 12),
                Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Text(
                        "${widget.arguments.receiver}, here's a gift from ${widget.arguments.sender}!",
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                          fontSize: 16,
                        )),
                        textAlign: TextAlign.start)),
                const SizedBox(height: 24),
                Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Text(widget.arguments.message,
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        )),
                        textAlign: TextAlign.center)),
              ],
            ),
          ],
        ));
  }

  Widget buildImage(String urlImage, int index) => Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        clipBehavior: Clip.hardEdge,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
        child: FadeInImage.memoryNetwork(
          placeholder: kTransparentImage,
          image: urlImage,
          fit: BoxFit.cover,
        ),
      );

  Widget buildBottomBar(BuildContext context) => Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: SizedBox(
        height: 100,
        child: Center(
          child: ElevatedButton(
            onPressed: widget.arguments.cashAmount > 0
                ? _showPaymentDialog
                : () async {
                    final result = await shareImageFromAssets(
                        receiver: widget.arguments.receiver,
                        filename: 'giftbox.png',
                        assetPath: 'images/giftbox.png',
                        urlToShare: widget.arguments.toJsonString());

                    if (result && mounted) {
                      Navigator.pushNamed(
                          context, SenderConfirmation.routeName);
                    }
                  },
            child: Text(
              widget.arguments.cashAmount > 0 ? 'Add payment' : 'Send Gift',
              style: const TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ),
      ));

  Future<void> _showPaymentDialog() async {
    final String? value = await showModalBottomSheet<String?>(
      context: context,
      isScrollControlled: true,
      barrierColor: Colors.black87,
      builder: (context) => EgiftPaymentDialog(),
    );

    if (value == null || !mounted) return;

    final model = PaymentModel.fromString(value);
    final Uri deeplinkUri = Uri.parse(model.deeplink);

    final giftModel = EgiftPreviewArguments(
      imgUrl: widget.arguments.imgUrl,
      cashAmount: widget.arguments.cashAmount,
      sender: widget.arguments.sender,
      receiver: widget.arguments.receiver,
      message: widget.arguments.message,
      paymentUrl: model.name,
    );

    final canLaunch = await launchUrl(deeplinkUri);

    if (canLaunch) {
      await _showEgiftDialog(model, giftModel);
    } else {
      if (mounted) {
        Navigator.of(context)
            .push(
              MaterialPageRoute(
                builder: (context) => CheckOut(
                  url: Platform.isIOS ? model.iosUrl : model.androidUrl,
                ),
              ),
            )
            .then((value) => _showEgiftDialog(model, giftModel));
      }
    }
  }

  Future<void> _showEgiftDialog(
      PaymentModel model, EgiftPreviewArguments giftModel) async {
    final String? value = await showModalBottomSheet<String?>(
      context: context,
      isScrollControlled: true,
      barrierColor: Colors.black87,
      builder: (context) => EgiftDialogSend(
        receiver: giftModel.receiver,
        paymentName: model.name,
        giftURL: giftModel.toJsonString(),
      ),
    );

    if (value != null && mounted) {
      Navigator.pushNamed(context, SenderConfirmation.routeName);
    }
  }

  // Widget buildBottomBar() => Container(
  //     margin: const EdgeInsets.symmetric(vertical: 20),
  //     padding: const EdgeInsets.symmetric(horizontal: 32),
  //     child: Row(
  //       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
  //       children: [
  //         Expanded(
  // child: ElevatedButton(
  //   onPressed: () async {
  //     // EventTracker().track(
  //     //     'Tap Shop it!',
  //     //     properties: {
  //     //       'product_id': widget.product.id,
  //     //       'product_title': widget.product.title
  //     //     }
  //     // );

  //     Navigator.of(context).push(MaterialPageRoute(
  //         builder: (context) => CheckOut(
  //               url: widget.arguments.paymentUrl,
  //             )));
  //   },
  //   child: const Text(
  //     'Send payment',
  //     style: TextStyle(
  //       color: Colors.white,
  //     ),
  //   ),
  // ),
  //         ),
  //         const SizedBox(width: 10),
  //         Expanded(
  //           child: ElevatedButton(
  //             onPressed: () async {
  //               // EventTracker().track(
  //               //     'Tap Shop it!',
  //               //     properties: {
  //               //       'product_id': widget.product.id,
  //               //       'product_title': widget.product.title
  //               //     }
  //               // );

  // final bytes = await rootBundle.load('images/giftbox.png');
  // final list = bytes.buffer.asUint8List();

  // final tempDir = await getTemporaryDirectory();
  // final file = await File('${tempDir.path}/giftbox.png').create();
  // file.writeAsBytesSync(list);

  // final sharedUrl =
  //     'https://eldortoptal.github.io/${urlEncodeData(widget.arguments.toJsonString())}';
  // // final result = await Share.share('check out my website https://example.com', subject: 'Look what I made!');
  // print(sharedUrl);
  // final result = await Share.shareXFiles([XFile(file.path)],
  //     text: sharedUrl);

  // if (result.status == ShareResultStatus.success) {
  //   print('Thank you for sharing the picture!');
  // }
  //             },
  //             child: const Text(
  //               'Send eGift',
  //               style: TextStyle(
  //                 color: Colors.white,
  //               ),
  //             ),
  //           ),
  //         )
  //       ],
  //     ));
}
