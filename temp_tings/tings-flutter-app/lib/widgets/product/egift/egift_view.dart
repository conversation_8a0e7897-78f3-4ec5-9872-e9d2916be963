import 'dart:async';
import 'dart:convert';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:tings/widgets/product/check_out.dart';
import 'package:tings/widgets/product/data/product.dart';
import 'package:tings/widgets/product/edit_product.dart';
import 'package:tings/widgets/product/egift/custom_chip_widget.dart';
import 'package:tings/widgets/product/egift/egift_preview.dart';
import 'package:tings/widgets/product/product_carousel.dart';
import 'package:tings/widgets/product/purchase_bottom_sheet.dart';
import 'package:tings/widgets/product/reverse_purchase_bottom_sheet.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/custom_switch.dart';
import 'package:tings/widgets/shared/event_tracker.dart';
import 'package:tings/widgets/shared/utils.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/data/wishlist_item.dart';
import 'package:tings/widgets/wishlist/widgets/wishlist_button.dart';
import 'package:tings/widgets/wishlist/widgets/wishlist_details_form.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';
import 'package:transparent_image/transparent_image.dart';

class EgiftViewArguments {
  final Product product;
  final WishlistItem? wishlistItem;
  final bool? isOwner;

  EgiftViewArguments({required this.product, this.wishlistItem, this.isOwner});
}

class EgiftView extends StatefulWidget {
  final Product product;
  final WishlistItem? wishlistItem;
  final bool? isOwner;
  static const String routeName = '/egift';

  const EgiftView(
      {super.key, required this.product, this.wishlistItem, this.isOwner});

  @override
  State<StatefulWidget> createState() => _EgiftViewState();
}

class _EgiftViewState extends State<EgiftView> {
  int activeIndex = 0;
  static final prices = [0, 25, 50, 100];
  var init_prices = prices[1];
  var _updated = false;
  var urlImage = '';

  final TextEditingController _sender = TextEditingController();
  final TextEditingController _recipient = TextEditingController();
  final TextEditingController _amount =
      TextEditingController(text: '${prices[1]}');
  final TextEditingController _message = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  Timer? _debounce;

  @override
  void initState() {
    EventTracker().track('View Product Details', properties: {
      'product_id': widget.product.id,
      'product_title': widget.product.title
    });

    // _sendViewedToActivity();
    super.initState();

    _amount.addListener(() {
      // Update the state whenever the input changes
      setState(() {
        if (_amount.text.isEmpty) {
          init_prices = 0;
        } else {
          try {
            init_prices = double.parse(_amount.text).toInt();
          } catch (e) {
            init_prices = 0;
          }
        }
      });
    });
  }

  // void _sendViewedToActivity() async {
  //   await Api.post('/api/activity/viewed',
  //       body: {'product': widget.product.id});
  // }

  @override
  void dispose() {
    _sender.dispose();
    _recipient.dispose();
    _amount.dispose();
    _message.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(_updated),
        ),
      ),
      bottomNavigationBar: buildBottomBar(),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.only(bottom: 30),
          children: <Widget>[
            Stack(
              fit: StackFit.passthrough,
              children: <Widget>[
                CarouselSlider.builder(
                  itemCount: widget.product.images.isNotEmpty
                      ? widget.product.images.length
                      : 1,
                  options: CarouselOptions(
                    height: 378,
                    enlargeCenterPage: true,
                    enlargeStrategy: CenterPageEnlargeStrategy.height,
                    enableInfiniteScroll: false,
                  ),
                  itemBuilder: (context, index, realIndex) {
                    if (widget.product.images.isNotEmpty) {
                      urlImage = widget.product.images[index].url;
                      return buildImage(urlImage, index);
                    } else {
                      return Image.asset(
                        "images/product_placeholder.png",
                        width: 328,
                        height: 328,
                      );
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                    '\$${Utils.convertToCurrency(init_prices.toDouble())}',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 30, fontWeight: FontWeight.bold)),
                    textAlign: TextAlign.start)),
            const SizedBox(height: 12),
            const Padding(
                padding: EdgeInsets.symmetric(horizontal: 24),
                child: Text("Gift amount",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w700),
                    textAlign: TextAlign.start)),
            const SizedBox(height: 6),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: TextFormField(
                controller: _amount,
                // validator: (value) {
                //   if (value == null || value.isEmpty) {
                //     return 'Field is required';
                //   }
                //   return null;
                // },
                maxLength: 4,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.done,
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        decoration: TextDecoration.none)),
                decoration: const InputDecoration(
                  labelText: 'Amount',
                  prefix: Text('\$'),
                  // prefixIcon: Icon(Icons.attach_money),
                  errorStyle: TextStyle(height: 0),
                ),
              ),
            ),

            const SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: SizedBox(
                height: 50,
                child: ListView(
                  shrinkWrap: true,
                  scrollDirection: Axis.horizontal,
                  children: [
                    ...prices.map(
                      (value) {
                        return CustomChips(
                          label: '\$$value',
                          focusColor: Colors.black87,
                          isSelected: value == init_prices,
                          onTap: () {
                            setState(() {
                              init_prices = value;
                              _amount.text = '$value';
                            });
                          },
                        );
                      },
                    ).toList(),
                    const SizedBox(width: 24),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            const Padding(
                padding: EdgeInsets.symmetric(horizontal: 24),
                child: Text("Who's this gift from?",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w700),
                    textAlign: TextAlign.start)),
            const SizedBox(height: 6),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: TextFormField(
                controller: _sender,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Field is required';
                  }
                  return null;
                },
                maxLength: 30,
                textInputAction: TextInputAction.done,
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        decoration: TextDecoration.none)),
                decoration: const InputDecoration(
                  labelText: 'Sender',
                  errorStyle: TextStyle(height: 0),
                ),
              ),
            ),

            const Padding(
                padding: EdgeInsets.symmetric(horizontal: 24),
                child: Text("Who's this gift for?",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w700),
                    textAlign: TextAlign.start)),
            const SizedBox(height: 6),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: TextFormField(
                controller: _recipient,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Field is required';
                  }
                  return null;
                },
                maxLength: 30,
                textInputAction: TextInputAction.done,
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        decoration: TextDecoration.none)),
                decoration: const InputDecoration(
                  labelText: 'Receiver',
                  errorStyle: TextStyle(height: 0),
                ),
              ),
            ),
            const Padding(
                padding: EdgeInsets.symmetric(horizontal: 24),
                child: Text("Add a message (optional)",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w700),
                    textAlign: TextAlign.start)),
            const SizedBox(height: 6),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: TextField(
                controller: _message,
                textInputAction: TextInputAction.done,
                maxLines: 5,
                maxLength: 100,
                keyboardType: TextInputType.multiline,
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        decoration: TextDecoration.none)),
                decoration: const InputDecoration(
                  labelText: 'Message',
                  alignLabelWithHint: true,
                  errorStyle: TextStyle(height: 0),
                ),
              ),
            ),
            // const SizedBox(height: 24),
            // Container(
            //   padding: const EdgeInsets.symmetric(horizontal: 24),
            //   child: TextButton(
            //     onPressed: () async {

            //       if (!_formKey.currentState!.validate()) {
            //         return;
            //       }

            //     },
            //     style: ElevatedButton.styleFrom(
            //         backgroundColor: Colors.white,
            //         side: const BorderSide(color: Colors.grey),
            //     ),
            //     child: const Text('Preview gift', style: TextStyle(color: Colors.grey,),),
            //                 ),
            // ),
          ],
        ),
      ),
    );
  }

  Widget buildImage(String urlImage, int index) => Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        clipBehavior: Clip.hardEdge,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
        child: FadeInImage.memoryNetwork(
          placeholder: kTransparentImage,
          image: urlImage,
          fit: BoxFit.cover,
        ),
      );

  Widget buildBottomBar() => Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: SizedBox(
        height: 100,
        child: Center(
          child: ElevatedButton(
            onPressed: () async {
              if (!_formKey.currentState!.validate()) {
                return;
              }

              // EventTracker().track(
              //     'Tap Shop it!',
              //     properties: {
              //       'product_id': widget.product.id,
              //       'product_title': widget.product.title
              //     }
              // );

              Navigator.pushNamed(
                context,
                EgiftPreview.routeName,
                arguments: EgiftPreviewArguments(
                  imgUrl: urlImage,
                  cashAmount: init_prices.toDouble(),
                  sender: _sender.text.toString(),
                  receiver: _recipient.text.toString(),
                  message: _message.text.toString(),
                  paymentUrl: '',
                ),
              );
            },
            child: const Text(
              'Preview Gift',
              style: TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ),
      ));

  void showPurchaseBottomSheet(
      BuildContext context, bool showPurchaseDialog, bool showAnonymousDialog) {
    showModalBottomSheet<void>(
        context: context,
        isScrollControlled: true,
        barrierColor: Colors.black87,
        builder: (context) {
          return PurchaseBottomSheet(
              showAnonymousModal: showAnonymousDialog,
              showPurchaseModal: showPurchaseDialog,
              onClose: (purchased, anonymous) async {
                if (purchased) {
                  EventTracker().track('Tap Marked as purchased', properties: {
                    'product_id': widget.product.id,
                    'product_title': widget.product.title
                  });
                }
                Navigator.of(context).pop(true);
              });
        });
  }
}
