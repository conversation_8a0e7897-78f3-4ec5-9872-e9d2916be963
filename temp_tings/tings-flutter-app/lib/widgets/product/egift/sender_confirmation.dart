import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tings/blocs/bottom_bar_cubit/bottom_bar_cubit.dart';
import 'package:tings/widgets/product/egift/dialog/egift_dialog_preview.dart';
import 'package:tings/widgets/product/egift/egift_preview.dart';
import 'package:tings/widgets/product/egift/utils/data_format.dart';

class SenderConfirmation extends StatelessWidget {
  const SenderConfirmation({super.key});

  static const String routeName = '/senderconfirmation';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
                height: 24, width: 24),
            onPressed: () => Navigator.of(context).pop(),
          ),
          actionsIconTheme: const IconThemeData(color: Colors.black87),
          iconTheme: const IconThemeData(opacity: 1, color: Colors.black87),
        ),
        bottomNavigationBar: buildBottomBar(context),
        body: Container(
          height: double.infinity,
          width: double.infinity,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('images/sender_confirmation.gif'),
              fit: BoxFit.cover,
            ),
          ),
        ));
  }

  Widget buildBottomBar(BuildContext context) => Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: SizedBox(
        height: 100,
        child: Center(
          child: ElevatedButton(
            onPressed: () {
              context.read<BottomBarCubit>().changePage();
              Navigator.popUntil(context, (route) => route.isFirst);
            },
            child: const Text(
              'Continue Exploring',
              style: TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ),
      ));
}
