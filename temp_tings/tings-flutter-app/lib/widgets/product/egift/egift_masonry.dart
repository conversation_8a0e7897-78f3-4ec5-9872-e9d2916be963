import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/product/data/product.dart';
import 'package:tings/widgets/product/egift/egift_view.dart';
import 'package:tings/widgets/product/product_view.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/shimmer_loading.dart';
import 'package:tings/widgets/wishlist/data/wishlist_item.dart';
import 'package:tings/widgets/wishlist/widgets/wishlist_button.dart';


class EgiftMasonry extends StatelessWidget {
  const EgiftMasonry(
      {super.key,
      required this.products,
      required this.controller,
      this.onReturnFromDetails,
      this.onClose,
      this.isOwner,
      this.wishlistItems});

  final ScrollController controller;
  final List<Product> products;
  final VoidCallback? onClose;
  final Function(bool)? onReturnFromDetails;
  final bool? isOwner;
  final List<WishlistItem>? wishlistItems;

  @override
  Widget build(BuildContext context) {
    return MasonryGridView.count(
      controller: controller,
      itemCount: products.length,
      padding: const EdgeInsets.only(bottom: 50, left: 16, right: 16),
      // the number of columns
      crossAxisCount: 2,
      // vertical gap between two items
      mainAxisSpacing: 10,
      // horizontal gap between two items
      crossAxisSpacing: 10,
      itemBuilder: (context, index) {
        double deviceWidth = MediaQuery.of(context).size.width;
        var padding = MediaQuery.of(context).padding;
        double colWidth = (deviceWidth - padding.left - padding.right) / 2 - 21;
        var imgHeight = products[index].thumbnail?.height?.toDouble() ?? 150;
        var imgWidth = products[index].thumbnail?.width?.toDouble() ?? colWidth;
        var ratio = colWidth / imgWidth;
        var height = imgHeight * ratio;
        
        // display each item with a card
        return GestureDetector(
          onTap: () {
            var item = wishlistItems?.firstWhere(
                (element) => element.product.id == products[index].id);
            Navigator.pushNamed(
              context,
              EgiftView.routeName,
              arguments: EgiftViewArguments(
                  product: products[index],
                  wishlistItem: item,
                  isOwner: isOwner),
            ).then((value) => {
                  if (onReturnFromDetails != null)
                    {onReturnFromDetails!(value as bool)}
                });
          },
          child: Container(
            clipBehavior: Clip.hardEdge,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(4)),
            ),
            child: Stack(
              fit: StackFit.passthrough,
              children: <Widget>[
                CachedNetworkImage(
                  imageUrl: products[index].thumbnail?.url ?? '',
                  placeholder: (context, url) => ShimmerLoading(
                    linearGradient: shimmerGradient,
                    child: Container(
                        width: colWidth,
                        height: height,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(4),
                        )),
                  ),
                  errorWidget: (context, url, error) => Image.asset(
                    "images/product_placeholder.png",
                    width: 328,
                  ),
                ),
                // Conditional(
                //   condition: products[index].label != null,
                //   child: Positioned(
                //     // red box
                //     left: 0,
                //     top: 8,
                //     child: Row(
                //       children: [
                //         Container(
                //           padding: const EdgeInsets.only(
                //               top: 7, bottom: 7, left: 9, right: 5),
                //           color: const Color(0xFFFFC107),
                //           child: Text(
                //             products[index].label ?? '',
                //             style: GoogleFonts.inter(
                //                 textStyle: const TextStyle(
                //                     fontSize: 12,
                //                     color: Colors.black,
                //                     fontWeight: FontWeight.w600)),
                //           ),
                //         ),
                //         Image.asset('images/arrow_back_yellow.png', height: 29),
                //       ],
                //     ),
                //   ),
                // ),
                // Positioned(
                //   // red box
                //   right: 8,
                //   top: 8,
                //   child: WishlistButton(
                //     active: isOwner == true ? true : null,
                //     product: products[index],
                //     onClose: (didChange) {
                //       if (didChange && onClose != null) {
                //         onClose!();
                //       }
                //     },
                //   ),
                // ),
              ],
            ),
          ),
        );
      },
    );
  }
}
