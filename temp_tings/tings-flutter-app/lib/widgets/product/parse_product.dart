import 'package:debounce_throttle/debounce_throttle.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart';
import 'package:tings/widgets/product/data/parsed_product.dart';
import 'package:tings/widgets/product/parsed_product_form.dart';
import 'package:tings/widgets/product/parsed_product_info.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../shared/api.dart';

class ParseProductArguments {
  final String url;
  final Wishlist? wishlist;

  ParseProductArguments({this.wishlist, required this.url});
}

class ParseProduct extends StatefulWidget {
  final String url;
  final Wishlist? wishlist;
  static const String routeName = '/parse-product';

  const ParseProduct({super.key, required this.url, this.wishlist});

  @override
  State<ParseProduct> createState() => _ParseProductState();
}

class _ParseProductState extends State<ParseProduct> {
  var loadingPercentage = 0;
  late final WebViewController controller;
  String _stateText = "It’ll be added in no time...";
  ParsedProduct? _product;
  int _step = 1;
  bool _called = false;
  bool _loading = false;
  bool _error = false;
  final debouncer =
      Debouncer<String>(const Duration(seconds: 5), initialValue: '');

  @override
  void initState() {
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(NavigationDelegate(
        onPageStarted: (url) {
          setState(() {
            loadingPercentage = 0;
            _loading = true;
          });
        },
        onProgress: (progress) {
          setState(() {
            loadingPercentage = progress;
            if (progress > 90 && !_called) {
              _called = true;
              loadingPercentage = 100;
              readJS();
            }
          });
        },
        onPageFinished: (url) {
          /*setState(() {
            loadingPercentage = 100;
          });
          readJS();*/
        },
      ))
      ..loadRequest(
        Uri.parse(widget.url),
      );
    debouncer.values.listen(sendHtml);
    super.initState();
  }

  void readJS() async {
    setState(() {
      _stateText = "Almost there...";
      _step = 2;
      _loading = true;
    });
    var html = await controller.runJavaScriptReturningResult(
        "window.document.getElementsByTagName('html')[0].outerHTML");
    var replaced = html.toString().replaceAll("\\u003C", '<');
    debouncer.value = replaced;
  }

  void sendHtml(String html) async {
    setState(() {
      _stateText = "Adding now...";
      _step = 3;
      _loading = true;
    });
    Response response = await Api.post('/api/parse_product',
        body: {"url": widget.url, "dom": html});
    if (response.statusCode == 200) {
      var product = Api.parse(response.bodyBytes, ParsedProduct.fromJson);
      setState(() {
        _product = product;
        _loading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Success')),
      );
    } else {
      setState(() {
        _error = true;
        _loading = false;
      });
//      throw Exception('Failed to parse html');
    }
  }

  void showInfoDialog() {
    showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
            scrollable: true,
            content: ParsedProductInfo(onClose: (hide){
              Navigator.of(context).pop();
            }, showCheckbox: false,)
        )
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Add to Tings',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        actions: [
          IconButton(
              onPressed: () {
                showInfoDialog();
              },
              icon:
              Image.asset('images/icons/question-circle.png', height: 32, width: 32))
        ],
        centerTitle: true,
      ),
      body: Stack(children: [
        WebViewWidget(
          controller: controller,
        ),
        Conditional(
          condition: _product != null && !_loading,
          alternate: Container(
            color: Color(0xEDFFFFFF),
            child: Center(
              child: ListView(
                padding: EdgeInsets.symmetric(horizontal: 20),
                children: [
                  const SizedBox(
                    height: 62,
                  ),
                  Text(
                    'Adding item to',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 20,
                            color: Colors.black,
                            fontWeight: FontWeight.w700)),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  Image.asset(
                    "images/logo.png",
                    width: 81,
                    height: 47,
                  ),
                  const SizedBox(
                    height: 88,
                  ),
                  Progress(active: _step),
                  const SizedBox(
                    height: 26,
                  ),
                  Text(
                    _stateText,
                    style: GoogleFonts.inter(
                        textStyle: TextStyle(
                            fontSize: 20,
                            color: Colors.black,
                            fontWeight: FontWeight.w700)),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(
                    height: 131,
                  ),
                  if (_error)
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        'Try loading again',
                        style: GoogleFonts.inter(
                            textStyle: TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                        )),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
            ),
          ),
          child: ParsedProductForm(product: _product, wishlist: widget.wishlist, checkoutUrl: widget.url, showInfo: true),
        )
      ]),
    );
  }
}

class Progress extends StatelessWidget {
  final int active;

  const Progress({super.key, required this.active});

  @override
  Widget build(BuildContext context) {
    return Row(mainAxisAlignment: MainAxisAlignment.center, children: [
      Image.asset(
        "images/progress_check.png",
        width: 45,
        height: 45,
      ),
      SizedBox(
        height: 10,
        width: 65,
        child: Container(
          color: active > 1 ? Color(0xFFFFC107) : Color(0xFF969696),
        ),
      ),
      Image.asset(
        active > 1
            ? "images/progress_check.png"
            : "images/progress_uncheck.png",
        width: 45,
        height: 45,
      ),
      SizedBox(
        height: 10,
        width: 65,
        child: Container(
          color: active > 2 ? Color(0xFFFFC107) : Color(0xFF969696),
        ),
      ),
      Image.asset(
        active > 2
            ? "images/progress_check.png"
            : "images/progress_uncheck.png",
        width: 45,
        height: 45,
      ),
    ]);
  }
}
