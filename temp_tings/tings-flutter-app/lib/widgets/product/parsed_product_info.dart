import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/shared/conditional.dart';

import '../shared/checkbox_option.dart';

class ParsedProductInfo extends StatefulWidget {
  final Function(bool) onClose;
  final bool showCheckbox;

  const ParsedProductInfo({super.key, required this.onClose, required this.showCheckbox});

  @override
  State<StatefulWidget> createState() => _ParsedProductInfoState();
}

class _ParsedProductInfoState extends State<ParsedProductInfo> {
  bool hideForFuture = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('This feature is still being improved. You may see minor bugs but we\'re working on it!',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.w400)),
            textAlign: TextAlign.center),
        const SizedBox(
          height: 15,
        ),
        Text('Make sure you…',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.w700)),
            textAlign: TextAlign.left),
        SizedBox(
          height: 10,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                clipBehavior: Clip.hardEdge,
                decoration: const BoxDecoration(
                  borderRadius:
                  BorderRadius.all(Radius.circular(90)),
                  color: Color(0xFFFFC107),
                ),
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Center(
                    child: Text(
                        '1',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                                fontSize: 14,
                                color: Colors.white,
                                fontWeight: FontWeight.w600)),
                        textAlign: TextAlign.center),
                  ),
                ),
            ),
            const SizedBox(
              width: 16,
            ),
            Flexible(
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                      )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'Review the product title and price',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                        ' (if it’s not right, update it!)'),
                  ],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 16,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              clipBehavior: Clip.hardEdge,
              decoration: const BoxDecoration(
                borderRadius:
                BorderRadius.all(Radius.circular(90)),
                color: Color(0xFFFFC107),
              ),
              child: SizedBox(
                width: 24,
                height: 24,
                child: Center(
                  child: Text(
                      '2',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 14,
                              color: Colors.white,
                              fontWeight: FontWeight.w600)),
                      textAlign: TextAlign.center),
                ),
              ),
            ),
            const SizedBox(
              width: 16,
            ),
            Flexible(
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                      )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'Add your products to your preferred wishlist',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                        ' using the dropdown menu'),
                  ],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 16,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              clipBehavior: Clip.hardEdge,
              decoration: const BoxDecoration(
                borderRadius:
                BorderRadius.all(Radius.circular(90)),
                color: Color(0xFFFFC107),
              ),
              child: SizedBox(
                width: 24,
                height: 24,
                child: Center(
                  child: Text(
                      '3',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 14,
                              color: Colors.white,
                              fontWeight: FontWeight.w600)),
                      textAlign: TextAlign.center),
                ),
              ),
            ),
            const SizedBox(
              width: 16,
            ),
            Flexible(
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                      )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'Add the right product image!',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                        ' If the image is wrong, take a screenshot, click edit, and add it from your photos'),
                  ],
                ),
              ),
            ),
          ],
        ),

        Conditional(
          condition: widget.showCheckbox,
          child: Padding(
            padding: const EdgeInsets.only(top: 16),
            child: CheckboxOption(
              text: 'Don\'t show me this message again',
              value: hideForFuture,
              onChanged: (value) => {
                setState((){
                  hideForFuture = !hideForFuture;
                })
              },
            ),
          ),
        ),

        Padding(
          padding:
              const EdgeInsets.only(top: 20),
          child: ElevatedButton(
            onPressed: () {widget.onClose(hideForFuture);},
            style: const ButtonStyle(
                padding: MaterialStatePropertyAll(
                    EdgeInsets.symmetric(horizontal: 50, vertical: 15))),
            child: Text(
              'Got it!',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.w600)),
            ),
          ),
        )
      ],
    );
  }
}
