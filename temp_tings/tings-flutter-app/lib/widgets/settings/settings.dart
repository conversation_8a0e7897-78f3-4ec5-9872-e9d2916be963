import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tings/widgets/profile/profile_form.dart';
import 'package:tings/widgets/settings/terms.dart';
import 'package:tings/widgets/shared/custom_switch.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../shared/api.dart';
import '../shared/event_tracker.dart';
import '../welcome.dart';
import 'privacy.dart';

class Settings extends StatelessWidget {
  Settings({super.key}) {
    EventTracker().track('View Settings');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Settings',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 32),
        child: GestureDetector(
          onTap: () {
            logout(context);
          },
          child: Text(
            'Log out',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
              fontSize: 18,
              color: Colors.black,
            )),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 34),
        child: Column(
          children: [
            ListTile(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => ProfileForm(
                              continueButtonLabel: 'Save',
                              onComplete: () {},
                            )));
              },
              contentPadding: const EdgeInsets.symmetric(horizontal: 8),
              trailing: Image.asset('images/iconly/Light/ArrowRight2.png',
                  height: 24, width: 24),
              title: Text(
                'Profile',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 18,
                  color: Colors.black,
                )),
              ),
            ),
            // const Divider(color: Color(0xFFD6D6D6), thickness: 1),
            // ListTile(
            //   onTap: () {
            //     Navigator.push(context,
            //         MaterialPageRoute(builder: (context) => const _Settings()));
            //   },
            //   contentPadding: const EdgeInsets.symmetric(horizontal: 8),
            //   trailing: Image.asset('images/iconly/Light/ArrowRight2.png',
            //       height: 24, width: 24),
            //   title: Text(
            //     'Settings',
            //     style: GoogleFonts.inter(
            //         textStyle: const TextStyle(
            //       fontSize: 18,
            //       color: Colors.black,
            //     )),
            //   ),
            // ),
            const Divider(color: Color(0xFFD6D6D6), thickness: 1),
            ListTile(
              onTap: () {
                Navigator.push(context,
                    MaterialPageRoute(builder: (context) => const Terms()));
              },
              contentPadding: const EdgeInsets.symmetric(horizontal: 8),
              trailing: Image.asset('images/iconly/Light/ArrowRight2.png',
                  height: 24, width: 24),
              title: Text(
                'Terms & Conditions',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 18,
                  color: Colors.black,
                )),
              ),
            ),
            const Divider(color: Color(0xFFD6D6D6), thickness: 1),
            ListTile(
              onTap: () {
                Navigator.push(context,
                    MaterialPageRoute(builder: (context) => const Privacy()));
              },
              contentPadding: const EdgeInsets.symmetric(horizontal: 8),
              trailing: Image.asset('images/iconly/Light/ArrowRight2.png',
                  height: 24, width: 24),
              title: Text(
                'Privacy Policy',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 18,
                  color: Colors.black,
                )),
              ),
            ),
            // const Divider(color: Color(0xFFD6D6D6), thickness: 1),
            // ListTile(
            //   contentPadding: const EdgeInsets.symmetric(horizontal: 8),
            //   title: Text(
            //     'Share app',
            //     style: GoogleFonts.inter(
            //         textStyle: const TextStyle(
            //       fontSize: 18,
            //       color: Colors.black,
            //     )),
            //   ),
            // ),
            const Divider(color: Color(0xFFD6D6D6), thickness: 1),
            ListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 8),
              subtitle: Text(
                '<EMAIL>',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 18,
                  height: 1.6,
                  color: Colors.black,
                )),
              ),
              title: Text(
                'Contact us',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 12,
                  color: Colors.black,
                )),
              ),
            ),
            const Divider(color: Color(0xFFD6D6D6), thickness: 1),
            ListTile(
              onTap: () {
                onDeleteAccountTap(context)
                .then((value) async {
                  if (value == true) {
                    var user = FirebaseAuth.instance.currentUser;
                    if (user?.email != null) {
                      await Api.post('/api/account_deletion', body: {'email': user!.email});
                      await logout(context);
                    }
                  }
                });
              },
              contentPadding: const EdgeInsets.symmetric(horizontal: 8),
              title: Text(
                'Delete account',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 18,
                  color: Colors.red,
                )),
              ),
            ),
            const Divider(color: Color(0xFFD6D6D6), thickness: 1),
            const _AppVersion(),
          ],
        ),
      ),
    );
  }

  Future logout(BuildContext context) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      var prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      await WishlistDataProvider.clear();
      await FirebaseAuth.instance.signOut();
      return Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => Welcome()));
    }
  }

  Future onDeleteAccountTap(BuildContext context) {
    return showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          title: Text(
            'Delete event',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 16, fontWeight: FontWeight.w700)),
            textAlign: TextAlign.left,
          ),
          content: Text(
            'Are you sure you want to delete your account?\nWe will send account deletion validation email to your address.',
            style: GoogleFonts.inter(
                textStyle:
                const TextStyle(color: Colors.black, fontSize: 16)),
            textAlign: TextAlign.left,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false);
              },
              child: const Text('Cancel',
                  style: TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                      decoration: TextDecoration.underline)),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(true);
              },
              child: const Text('Send email',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.red,
                  )),
            ),
          ],
        ));
  }
}

class _AppVersion extends StatefulWidget {
  const _AppVersion({super.key});

  @override
  State<StatefulWidget> createState() => _AppVersionState();
}

class _AppVersionState extends State<_AppVersion> {
  String _version = '';

  @override
  void initState() {
    PackageInfo.fromPlatform().then((PackageInfo packageInfo) {
      setState(() {
        _version = '${packageInfo.version}+${packageInfo.buildNumber}';
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 8),
      subtitle: Text(
        _version,
        style: GoogleFonts.inter(
            textStyle: const TextStyle(
              fontSize: 18,
              height: 1.6,
              color: Colors.black,
            )),
      ),
      title: Text(
        'Version',
        style: GoogleFonts.inter(
            textStyle: const TextStyle(
              fontSize: 12,
              color: Colors.black,
            )),
      ),
    );
  }
}

class _Settings extends StatefulWidget {
  const _Settings({super.key});

  @override
  State<StatefulWidget> createState() => _SettingsState();
}

class _SettingsState extends State<_Settings> {
  bool _allowSMS = false;
  bool _allowNotifications = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Settings',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 34),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Notifications',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 12,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 8,
            ),
            InkWell(
              onTap: () {
                setState(() {
                  _allowNotifications = !_allowNotifications;
                });
              },
              child: Row(
                children: [
                  Expanded(
                    child: Text('Allow push notifications',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                                fontSize: 18, color: Colors.black))),
                  ),
                  SizedBox(
                    width: 38,
                    child: CustomSwitch(
                      value: _allowNotifications,
                      onChanged: (bool value) {
                        setState(() {
                          _allowNotifications = value;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 24,
            ),
            InkWell(
              onTap: () {
                setState(() {
                  _allowSMS = !_allowSMS;
                });
              },
              child: Row(
                children: [
                  Expanded(
                    child: Text('Allow SMS',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                                fontSize: 18, color: Colors.black))),
                  ),
                  SizedBox(
                    width: 38,
                    child: CustomSwitch(
                      value: _allowSMS,
                      onChanged: (bool value) {
                        setState(() {
                          _allowSMS = value;
                        });
                      },
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
