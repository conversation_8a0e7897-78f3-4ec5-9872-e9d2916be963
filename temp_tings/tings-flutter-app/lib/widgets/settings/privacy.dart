import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class Privacy extends StatelessWidget {
  const Privacy({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Privacy Policy',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: <PERSON>View(
          children: [
            Text(
              'The Gifts Company LLC Privacy Policy',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 24,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              )),
              textAlign: TextAlign.center,
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'Last updated: November 18, 2022',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
              textAlign: TextAlign.center,
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'This Privacy Policy describes Our policies and procedures on the collection, use and disclosure of Your information when You use the Service and tells You about Your privacy rights and how the law protects You.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'We use Your Personal data to provide and improve the Service. By using the Service, You agree to the collection and use of information in accordance with this Privacy Policy. This Privacy Policy has been created with the help of the Free Privacy Policy Generator.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 34,
            ),
            Text(
              'Interpretation and Definitions',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 20,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Interpretation',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'The words of which the initial letter is capitalized have meanings defined under the following conditions. The following definitions shall have the same meaning regardless of whether they appear in singular or in plural.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 20,
            ),
            Text(
              'Definitions',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'For the purposes of this Privacy Policy:',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'Account',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(
                      text:
                          ' means a unique account created for You to access our Service or parts of our Service.'),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'Affiliate',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(
                      text:
                          ' means an entity that controls, is controlled by or is under common control with a party, where "control" means ownership of 50% or more of the shares, equity interest or other securities entitled to vote for election of directors or other managing authority.'),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'Application',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(
                      text:
                          ' means the software program provided by the Company downloaded by You on any electronic device, named ‘Tings’'),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'Company',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(
                      text:
                          ' (referred to as either "the Company", "We", "Us" or "Our" in this Agreement) refers to The Gifts Company LLC, 208 W. State Street Trenton, New Jersey 08608.'),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'Country',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(text: ' refers to: United States of America'),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'Device',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(
                      text:
                          ' means any device that can access the Service such as a computer, a cellphone or a digital tablet.'),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'Personal Data',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(
                      text:
                          ' is any information that relates to an identified or identifiable individual.'),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'Service',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(text: ' refers to the Application.'),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'Service Provider',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(
                      text:
                          ' means any natural or legal person who processes the data on behalf of the Company. It refers to third-party companies or individuals employed by the Company to facilitate the Service, to provide the Service on behalf of the Company, to perform services related to the Service or to assist the Company in analyzing how the Service is used.'),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'Third-party Social Media Service',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(
                      text:
                          ' refers to any website or any social network website through which a User can log in or create an account to use the Service.'),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'Usage Data',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(
                      text:
                          ' refers to data collected automatically, either generated by the use of the Service or from the Service infrastructure itself (for example, the duration of a page visit).'),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
                children: const <TextSpan>[
                  TextSpan(
                      text: 'You',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(
                      text:
                          ' means the individual accessing or using the Service, or the company, or other legal entity on behalf of which such individual is accessing or using the Service, as applicable.'),
                ],
              ),
            ),
            const SizedBox(
              height: 30,
            ),
            Text(
              'Collecting and Using Your Personal Data',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 20,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              )),
            ),
            const SizedBox(
              height: 20,
            ),
            Text(
              'Types of Data Collected',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 16,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Personal Data',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'While using Our Service, We may ask You to provide Us with certain personally identifiable information that can be used to contact or identify You. Personally identifiable information may include, but is not limited to:',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Email address',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'First name and last name',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Phone number',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Address, State, Province, ZIP/Postal code, City',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Usage Data',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Personal Data',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Usage Data is collected automatically when using the Service.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'Your access to and use of the Service is conditioned on Your acceptance of and compliance with these Terms and Conditions. These Terms and Conditions apply to all visitors, users and others who access or use the Service.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'Usage Data may include information such as Your Device\'s Internet Protocol address (e.g. IP address), browser type, browser version, the pages of our Service that You visit, the time and date of Your visit, the time spent on those pages, unique device identifiers and other diagnostic data.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'When You access the Service by or through a mobile device, We may collect certain information automatically, including, but not limited to, the type of mobile device You use, Your mobile device unique ID, the IP address of Your mobile device, Your mobile operating system, the type of mobile Internet browser You use, unique device identifiers and other diagnostic data.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'We may also collect information that Your browser sends whenever You visit our Service or when You access the Service by or through a mobile device.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Information from Third-Party Services',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'The Company allows You to create an account and log in to use the Service through the following Third-party Services:',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Google',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Facebook',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Apple ID',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'If You decide to register through or otherwise grant us access to a Third-Party Service, We may collect Personal data that is already associated with Your Third-Party Service\'s account, such as Your name, Your email address, Your activities or Your contact list associated with that account.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'You may also have the option of sharing additional information with the Company through Your Third-Party Service\'s account. If You choose to provide such information and Personal Data, during registration or otherwise, You are giving the Company permission to use, share, and store it in a manner consistent with this Privacy Policy.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Information Collected while Using the Application',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'While using Our Application, in order to provide features of Our Application, We may collect, with Your prior permission:',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Information regarding your location',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Information from your Device\'s phone book (contacts list)',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Pictures and other information from your Device\'s camera and photo library',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'We use this information to provide features of Our Service, to improve and customize Our Service. The information may be uploaded to the Company\'s servers and/or a Service Provider\'s server or it may be simply stored on Your device.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'You can enable or disable access to this information at any time, through Your Device settings.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Use of Your Personal Data',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 16,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'The Company may use Personal Data for the following purposes:',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'To provide and maintain our Service,',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' including to monitor the usage of our Service.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'To manage Your Account:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' to manage Your registration as a user of the Service. The Personal Data You provide can give You access to different functionalities of the Service that are available to You as a registered user.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'For the performance of a contract:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' the development, compliance and undertaking of the purchase contract for the products, items or services You have purchased or of any other contract with Us through the Service.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'To contact You:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' To contact You by email, telephone calls, SMS, or other equivalent forms of electronic communication, such as a mobile application\'s push notifications regarding updates or informative communications related to the functionalities, products or contracted services, including the security updates, when necessary or reasonable for their implementation.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'To provide You',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' with news, special offers and general information about other goods, services and events which we offer that are similar to those that you have already purchased or enquired about unless You have opted not to receive such information.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'To manage Your requests:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text: ' To attend and manage Your requests to Us.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'For business transfers:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' We may use Your information to evaluate or conduct a merger, divestiture, restructuring, reorganization, dissolution, or other sale or transfer of some or all of Our assets, whether as a going concern or as part of bankruptcy, liquidation, or similar proceeding, in which Personal Data held by Us about our Service users is among the assets transferred.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'For other purposes:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' We may use Your information for other purposes, such as data analysis, identifying usage trends, determining the effectiveness of our promotional campaigns and to evaluate and improve our Service, products, services, marketing and your experience.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'We may share Your personal information in the following situations:',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'With Service Providers:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' We may share Your personal information with Service Providers to monitor and analyze the use of our Service, to contact You.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'For business transfers:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' We may share or transfer Your personal information in connection with, or during negotiations of, any merger, sale of Company assets, financing, or acquisition of all or a portion of Our business to another company.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'With Affiliates:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' We may share Your information with Our affiliates, in which case we will require those affiliates to honor this Privacy Policy. Affiliates include Our parent company and any other subsidiaries, joint venture partners or other companies that We control or that are under common control with Us.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'With business partners:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' We may share Your information with Our business partners to offer You certain products, services or promotions.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'With other users:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' when You share personal information or otherwise interact in the public areas with other users, such information may be viewed by all users and may be publicly distributed outside. If You interact with other users or register through a Third-Party Social Media Service, Your contacts on the Third-Party Social Media Service may see Your name, profile, pictures and description of Your activity. Similarly, other users will be able to view descriptions of Your activity, communicate with You and view Your profile.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: RichText(
                text: TextSpan(
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  )),
                  children: const <TextSpan>[
                    TextSpan(
                        text: 'With Your consent:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(
                        text:
                            ' We may disclose Your personal information for any other purpose with Your consent.'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Retention of Your Personal Data',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 16,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'The Company will retain Your Personal Data only for as long as is necessary for the purposes set out in this Privacy Policy. We will retain and use Your Personal Data to the extent necessary to comply with our legal obligations (for example, if we are required to retain your data to comply with applicable laws), resolve disputes, and enforce our legal agreements and policies.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'The Company will also retain Usage Data for internal analysis purposes. Usage Data is generally retained for a shorter period of time, except when this data is used to strengthen the security or to improve the functionality of Our Service, or We are legally obligated to retain this data for longer time periods.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Transfer of Your Personal Data',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 16,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Your information, including Personal Data, is processed at the Company\'s operating offices and in any other places where the parties involved in the processing are located. It means that this information may be transferred to — and maintained on — computers located outside of Your state, province, country or other governmental jurisdiction where the data protection laws may differ than those from Your jurisdiction.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'Your consent to this Privacy Policy followed by Your submission of such information represents Your agreement to that transfer.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'The Company will take all steps reasonably necessary to ensure that Your data is treated securely and in accordance with this Privacy Policy and no transfer of Your Personal Data will take place to an organization or a country unless there are adequate controls in place including the security of Your data and other personal information.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Delete Your Personal Data',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 16,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'You have the right to delete or request that We assist in deleting the Personal Data that We have collected about You.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'Our Service may give You the ability to delete certain information about You from within the Service.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'You may update, amend, or delete Your information at any time by signing in to Your Account, if you have one, and visiting the account settings section that allows you to manage Your personal information. You may also contact Us to request access to, correct, or delete any personal information that You have provided to Us.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'Please note, however, that We may need to retain certain information when we have a legal obligation or lawful basis to do so.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 30,
            ),
            Text(
              'Disclosure of Your Personal Data',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 16,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Business Transactions',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'If the Company is involved in a merger, acquisition or asset sale, Your Personal Data may be transferred. We will provide notice before Your Personal Data is transferred and becomes subject to a different Privacy Policy.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Law enforcement',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 16,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Under certain circumstances, the Company may be required to disclose Your Personal Data if required to do so by law or in response to valid requests by public authorities (e.g. a court or a government agency).',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Other legal requirements',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 16,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'The Company may disclose Your Personal Data in the good faith belief that such action is necessary to:',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Comply with a legal obligation',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Protect and defend the rights or property of the Company',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Prevent or investigate possible wrongdoing in connection with the Service',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Protect the personal safety of Users of the Service or the public',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                'Protect against legal liability',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                )),
              ),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Security of Your Personal Data',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 16,
                color: Colors.lightBlue,
                fontWeight: FontWeight.bold,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'The security of Your Personal Data is important to Us, but remember that no method of transmission over the Internet, or method of electronic storage is 100% secure. While We strive to use commercially acceptable means to protect Your Personal Data, We cannot guarantee its absolute security.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 30,
            ),
            Text(
              'Children\'s Privacy',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 20,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Our Service does not address anyone under the age of 13. We do not knowingly collect personally identifiable information from anyone under the age of 13. If You are a parent or guardian and You are aware that Your child has provided Us with Personal Data, please contact Us. If We become aware that We have collected Personal Data from anyone under the age of 13 without verification of parental consent, We take steps to remove that information from Our servers.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'If We need to rely on consent as a legal basis for processing Your information and Your country requires consent from a parent, We may require Your parent\'s consent before We collect and use that information.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 30,
            ),
            Text(
              'Links to Other Websites',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 20,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'Our Service may contain links to other websites that are not operated by Us. If You click on a third party link, You will be directed to that third party\'s site. We strongly advise You to review the Privacy Policy of every site You visit.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'We have no control over and assume no responsibility for the content, privacy policies or practices of any third party sites or services.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 30,
            ),
            Text(
              'Changes to this Privacy Policy',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 20,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'We may update Our Privacy Policy from time to time. We will notify You of any changes by posting the new Privacy Policy on our website.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'We will let You know via email and/or a prominent notice on Our Service, prior to the change becoming effective and update the "Last updated" date at the top of this Privacy Policy.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page.',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 30,
            ),
            Text(
              'Contact Us',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 20,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              )),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              'If you have any questions about this Privacy Policy, You can contact us:',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'By email: <EMAIL>',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'By visiting this page on our website: https://www.shoptings.com/contact',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              )),
            ),
            const SizedBox(
              height: 40,
            ),
          ],
        ),
      ),
    );
  }
}
