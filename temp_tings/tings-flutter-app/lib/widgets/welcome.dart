import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/auth/auth.dart';
import 'package:tings/widgets/profile/profile_form.dart';
import 'package:tings/widgets/home.dart';
import 'package:tings/widgets/shared/event_tracker.dart';

class Welcome extends StatelessWidget {
  Welcome({super.key}) {
    EventTracker().track('View Welcome');
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      Future.delayed(Duration.zero, () {
        Navigator.of(context).pushReplacement(
            MaterialPageRoute(
                builder: (context) => const HomePage()));
      });
    }
    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
            image: AssetImage("images/welcome_back.png"),
            fit: BoxFit.contain,
            alignment: Alignment.topCenter),
        color: Colors.white,
      ),
      child: Column(
        children: [
          const Expanded(
            child: SizedBox(),
          ),
          Container(
            decoration: const BoxDecoration(
                gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Colors.white10,
                Colors.transparent,
              ],
            )),
            child: const SizedBox(
              height: 50,
              width: 500,
            ),
          ),
          Container(
            decoration: const BoxDecoration(
                gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Colors.white,
                Colors.white10,
              ],
            )),
            child: const SizedBox(
              height: 150,
              width: 500,
            ),
            //child: const SizedBox(height: 150, width: 500,),
          ),
          Container(
            color: Colors.white,
            child: Column(
              children: [
                const SizedBox(
                  height: 15,
                ),
                Image.asset("images/logo.png", width: 114, height: 67),
                const SizedBox(
                  height: 15,
                ),
                Text(
                    'Show love to yourself and others\nthrough the power of gifting',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                            decoration: TextDecoration.none)),
                    textAlign: TextAlign.center),
                const SizedBox(
                  height: 8,
                ),
                Padding(
                  padding: const EdgeInsets.all(32),
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const HomePage()));
                    },
                    style: const ButtonStyle(
                        padding: MaterialStatePropertyAll(EdgeInsets.symmetric(
                            horizontal: 50, vertical: 15))),
                    child: Text(
                      'Lets go!',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 18, fontWeight: FontWeight.w600)),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                GestureDetector(
                    onTap: () {
                      final user = FirebaseAuth.instance.currentUser;
                      if (user != null) {
                        Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const ProfileForm()));
                      } else {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const Auth()));
                      }
                    },
                    child: Text('Sign in or Create Account',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                                decoration: TextDecoration.none)),
                        textAlign: TextAlign.center)),
                const SizedBox(
                  height: 40,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
