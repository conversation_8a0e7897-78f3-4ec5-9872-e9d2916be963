import 'dart:async';

import 'package:animations/animations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'package:tings/blocs/bottom_bar_cubit/bottom_bar_cubit.dart';
import 'package:tings/widgets/auth/auth.dart';
import 'package:tings/widgets/friends/friends.dart';
import 'package:tings/widgets/product/egift/dialog/egift_dialog.dart';
import 'package:tings/widgets/product/egift/egift_preview.dart';
import 'package:tings/widgets/product/parse_product.dart';
import 'package:tings/widgets/product/product_list.dart';
import 'package:tings/widgets/product/egift/utils/data_format.dart';
import 'package:tings/widgets/profile/profile_form.dart';
import 'package:tings/widgets/profile/profile_view.dart';
import 'package:tings/widgets/search/Search.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/wishlist/my_tings.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';
import 'package:uni_links/uni_links.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  // final String title;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  int? _friendsTabIndex = 0;
  late StreamSubscription _intentDataStreamSubscription;

  String _latestLink = '';

  Future<void> initUniLinks() async {
    uriLinkStream.listen((Uri? uri) {
      _latestLink = uri?.toString() ?? '';
      if (_latestLink.isNotEmpty) {
        String lastSegment = _latestLink.split('?').last;
        lastSegment = urlDecodeData(lastSegment);
        if (lastSegment.isNotEmpty) {
          final arguments = EgiftPreviewArguments.fromJsonString(lastSegment);
          Navigator.of(context).push(
            MaterialPageRoute(
              fullscreenDialog: true,
              builder: (context) => EgiftDialog(
                arguments: arguments,
              ),
            ),
          );
        }
      }
    }, onError: (err) {
      print(err);
    });
  }

  @override
  void initState() {
    initUniLinks();
    // For sharing or opening urls/text coming from outside the app while the app is in the memory
    _intentDataStreamSubscription =
        ReceiveSharingIntent.getTextStream().listen((String value) {
      if (value != null) {
        openParseProductWebView(value);
      }
    }, onError: (err) {
      print("getLinkStream error: $err");
    });

    // For sharing or opening urls/text coming from outside the app while the app is closed
    ReceiveSharingIntent.getInitialText().then(
      (value) {
        if (value != null) {
          openParseProductWebView(value);
        }
      },
    );

    FirebaseAuth.instance.authStateChanges().listen((User? user) {
      if (user == null) {
        print('User is currently signed out!');
      } else {
        print('User is signed in!');

        Api.get('/api/my_profile').then((response) {
          if (response.statusCode == 200 && response.body != '') {
            WishlistDataProvider.init();
          } else {
            redirectToProfileForm(context);
          }
        }).catchError((error) {
          redirectToProfileForm(context);
        });
      }
    });
    super.initState();
  }

  void openParseProductWebView(String url) {
    Navigator.pushNamed(
      context,
      ParseProduct.routeName,
      arguments: ParseProductArguments(url: url),
    );
  }

  void redirectToProfileForm(BuildContext context) {
    Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => const ProfileForm(
                  hideBackButton: true,
                )));
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return BlocListener<BottomBarCubit, BottomBarState>(
      listener: (context, state) {
        if (state is BottomBarChanged) {
          _onItemTapped(0);
        }
      },
      child: Scaffold(
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.shifting,
          items: const <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              backgroundColor: Colors.white,
              icon: ImageIcon(
                AssetImage("images/iconly/Light/Category.png"),
              ),
              activeIcon: ImageIcon(
                AssetImage("images/iconly/Bold/Category.png"),
              ),
              label: 'Explore',
            ),
            BottomNavigationBarItem(
              backgroundColor: Colors.white,
              icon: ImageIcon(
                AssetImage("images/iconly/Light/Heart.png"),
              ),
              activeIcon: ImageIcon(
                AssetImage("images/iconly/Bold/Heart.png"),
              ),
              label: 'MyTings',
            ),
            BottomNavigationBarItem(
              backgroundColor: Colors.white,
              icon: ImageIcon(
                AssetImage("images/iconly/Light/Search.png"),
              ),
              activeIcon: ImageIcon(
                AssetImage("images/iconly/Bold/Search.png"),
              ),
              label: 'Search',
            ),
            BottomNavigationBarItem(
              backgroundColor: Colors.white,
              icon: ImageIcon(
                AssetImage("images/iconly/Light/3_User.png"),
              ),
              activeIcon: ImageIcon(
                AssetImage("images/iconly/Bold/3_User.png"),
              ),
              label: 'Connect',
            ),
            BottomNavigationBarItem(
              backgroundColor: Colors.white,
              icon: ImageIcon(
                AssetImage("images/iconly/Light/Profile.png"),
              ),
              activeIcon: ImageIcon(
                AssetImage("images/iconly/Bold/Profile.png"),
              ),
              label: 'Me',
            ),
          ],
          currentIndex: _selectedIndex,
          onTap: _onItemTapped,
        ),
        body: Center(
            // Center is a layout widget. It takes a single child and positions it
            // in the middle of the parent.
            child: PageTransitionSwitcher(
                transitionBuilder: (child, animation, secondaryAnimation) {
                  return FadeScaleTransition(
                    animation: animation,
                    // secondaryAnimation: secondaryAnimation,
                    child: child,
                  );
                },
                child: _NavigationDestinationView(
                  key: UniqueKey(),
                  index: _selectedIndex,
                  onFollowersTap: (_) {},
                ))),
      ),
    );
  }
}

class _NavigationDestinationView extends StatelessWidget {
  final Future Function(int? friendsTabIndex) onFollowersTap;

  const _NavigationDestinationView(
      {Key? key,
      required this.index,
      this.friendsTabIndex,
      required this.onFollowersTap})
      : super(key: key);

  final int index;
  final int? friendsTabIndex;

  static const TextStyle optionStyle =
      TextStyle(fontSize: 30, fontWeight: FontWeight.bold);

  static const List<Widget> _widgetOptions = <Widget>[
    ProductList(centerTitle: false),
    MyTings(),
    Search(),
    Friends(),
    //AuthTest(),
    ProfileForm(),
  ];

  Widget getOption(int index) {
    final user = FirebaseAuth.instance.currentUser;
    if (index == 1) {
      return user != null ? const MyTings() : const Auth();
    }
    if (index == 3) {
      return user != null ? Friends(startIndex: friendsTabIndex) : const Auth();
    }
    if (index == 4) {
      return user != null
          ? ProfileView(onFollowersTap: onFollowersTap)
          : const Auth();
    }
    return _widgetOptions.elementAt(index);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Center(
        child: getOption(index),
      ),
    );
  }
}
