import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/profile/data/profile.dart';

import 'profile_avatar.dart';

class ProfileList extends StatelessWidget {
  final List<Profile> items;
  final double? avatarRadius;
  final Widget Function(Profile)? getTitle;
  final Widget Function(Profile)? getSubtitle;
  final Function(Profile)? onTap;
  final Function(Profile)? onActiveButtonPress;
  final Function(Profile)? onButtonPress;
  final bool Function(Profile)? showButton;
  final bool Function(Profile)? activeButtonCondition;
  final String? activeButtonLabel;
  final double? activeButtonWidth;
  final double? buttonWidth;
  final String? buttonLabel;

  const ProfileList(
      {super.key,
      required this.items,
      this.avatarRadius,
      this.getTitle,
      this.getSubtitle,
      this.showButton,
      this.onTap,
      this.activeButtonCondition,
      this.activeButtonLabel,
      this.buttonLabel,
      this.onActiveButtonPress,
      this.onButtonPress,
      this.activeButtonWidth,
      this.buttonWidth});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: ClampingScrollPhysics(),
      itemBuilder: (context, index) => _buildLine(items[index]),
      separatorBuilder: (context, index) => const SizedBox(
        height: 10,
      ),
      itemCount: items.length,
    );
  }

  Widget _buildLine(Profile profile) {
    return ListTile(
      onTap: () {
        if (onTap != null) {
          onTap!(profile);
        }
      },
      title: getTitle != null
          ? getTitle!(profile)
          : Text(profile.displayName,
              style: const TextStyle(fontSize: 14, color: Colors.black)),
      subtitle: getSubtitle != null
          ? getSubtitle!(profile)
          : Text(
              profile.username,
              style: const TextStyle(fontSize: 12, color: Color(0xB2000000)),
            ),
      leading: ProfileAvatar(
        profile: profile,
        radius: avatarRadius ?? 20,
      ),
      trailing: TrailingButton(profile),
    );
  }

  Widget? TrailingButton(Profile profile) {
    if (showButton != null && showButton!(profile) == false) {
      return null;
    }

    if (activeButtonCondition != null) {
      if (activeButtonCondition!(profile)) {
        return SizedBox(
          width: buttonWidth ?? 79,
          height: 31,
          child: OutlinedButton(
            onPressed: () {
              if (onButtonPress != null) {
                onButtonPress!(profile);
              }
            },
            style: const ButtonStyle(
                padding: MaterialStatePropertyAll(
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8))),
            child: Text(
              buttonLabel ?? '',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 12,
                      color: Colors.black,
                      fontWeight: FontWeight.w600)),
            ),
          ),
        );
      } else {
        return SizedBox(
          width: activeButtonWidth ?? 56,
          height: 31,
          child: ElevatedButton(
            onPressed: () {
              if (onActiveButtonPress != null) {
                onActiveButtonPress!(profile);
              }
            },
            style: const ButtonStyle(
                padding: MaterialStatePropertyAll(
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8))),
            child: Text(
              activeButtonLabel ?? '',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w600)),
            ),
          ),
        );
      }
    }
    return null;
  }
}
