import 'package:tings/widgets/profile/data/profile.dart';

import '../../product/data/product.dart';

class Activity {
  final String id;
  final String owner;
  final String createdAt;
  final ActivityType type;
  final Product product;
  final Profile? wishListOwner;

  Activity(
      {required this.id,
      required this.owner,
      required this.createdAt,
      required this.type,
      this.wishListOwner,
      required this.product});

  factory Activity.fromJson(Map<String, dynamic> json) {
    return Activity(
      id: json['id'],
      owner: json['owner'],
      createdAt: json['createdAt'],
      type: ActivityType.fromString(json['type']),
      product: Product.fromJson(json['product']),
      wishListOwner: json['wishListOwner'] != null
          ? Profile.fromJson(json['wishListOwner'])
          : null,
    );
  }
}

enum ActivityType {
  viewed('VIEWED'),
  purchased('PURCHASED');

  const ActivityType(this.value);
  final String value;

  factory ActivityType.fromString(String val) {
    ActivityType type;

    if (val == ActivityType.viewed.value) {
      type = ActivityType.viewed;
    } else {
      type = ActivityType.purchased;
    }

    return type;
  }
}
