class Profile {
  String email;
  String displayName;
  String username;
  int? birthDay;
  int? birthMonth;
  String? zip;
  String? id;
  String? avatarUrl;

  Profile({
    required this.email,
    required this.displayName,
    required this.username,
    this.birthDay,
    this.birthMonth,
    this.id,
    this.zip,
    this.avatarUrl,
  });

  factory Profile.fromJson(Map<String, dynamic> json) {
    return Profile(
      id: json['id'],
      email: json['email'],
      displayName: json['displayName'],
      username: json['username'],
      birthDay: json['birthDay'],
      birthMonth: json['birthMonth'],
      zip: json['zip'],
      avatarUrl: json['avatarUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'username': username,
      'birthDay': birthDay,
      'birthMonth': birthMonth,
      'zip': zip,
      'avatarUrl': avatarUrl,
    };
  }
}
