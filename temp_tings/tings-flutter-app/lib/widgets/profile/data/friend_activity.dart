import 'template_variable.dart';

class FriendActivity {
  final String profileId;
  final String message;
  final String createdAt;
  final String displayName;
  final String? id;
  final String? avatarUrl;
  final String? oppositeProfileId;
  final String? template;
  final List<TemplateVariable>? templateVariables;

  FriendActivity(
      {required this.profileId,
      required this.message,
      required this.createdAt,
      required this.displayName,
      this.id,
      this.avatarUrl,
      this.template,
      this.templateVariables,
      this.oppositeProfileId,});

  factory FriendActivity.fromJson(Map<String, dynamic> json) {
    return FriendActivity(
      id: json['id'],
      profileId: json['profileId'],
      message: json['message'],
      createdAt: json['createdAt'],
      displayName: json['displayName'],
      avatarUrl: json['avatarUrl'],
      template: json['template'],
      templateVariables: json['templateVariables'] != null
          ? List<TemplateVariable>.from(
          (json['templateVariables'] as List<dynamic>).map((e) {
            return TemplateVariable.fromJson(e);
          })).toList()
          : null,
      oppositeProfileId: json['oppositeProfileId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'message': message,
      'createdAt': createdAt,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'template': template,
      'oppositeProfileId': oppositeProfileId,
    };
  }
}
