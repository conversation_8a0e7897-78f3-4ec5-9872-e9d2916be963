import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tings/widgets/friends/friend_followers_list.dart';
import 'package:tings/widgets/friends/friend_following_list.dart';
import 'package:tings/widgets/friends/friends.dart';
import 'package:tings/widgets/home.dart';
import 'package:tings/widgets/product/product_carousel.dart';
import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/profile/my_activity.dart';
import 'package:tings/widgets/profile/profile_avatar.dart';
import 'package:tings/widgets/settings/settings.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/followers_counter.dart';
import 'package:tings/widgets/shared/loading.dart';
import 'package:tings/widgets/shared/placeholder.dart';
import 'package:tings/widgets/wishlist/calendar.dart';
import 'package:tings/widgets/wishlist/upcoming_events.dart';

import '../shared/event_tracker.dart';
import '../shared/notifications_button.dart';
import 'data/activity.dart';
import 'notifications.dart';

class ProfileView extends StatefulWidget {
  final Future Function(int? friendsTabIndex) onFollowersTap;
  const ProfileView({super.key, required this.onFollowersTap});

  @override
  State<StatefulWidget> createState() => _ProfileViewState();
}

class _ProfileViewState extends State<ProfileView> {
  Profile? _profile;
  final List<Activity> _recentlyViewed = [];
  final List<Activity> _purchased = [];
  bool _loading = false;

  @override
  void initState() {
    init();
    EventTracker().track('View Me Page');
    _fetchProducts();
    super.initState();
  }

  void init() async {
    final response = await Api.get('/api/my_profile');
    if (response.statusCode == 200 && response.body != '') {
      setState(() {
        _profile = Api.parse(response.bodyBytes, Profile.fromJson);
      });
    } else {
      throw Exception('Failed to load profile');
    }
  }

  void _fetchProducts() async {
    setState(() {
      _loading = true;
    });
    final viewedResponse =
        await Api.get('/api/my_activity', queryParameters: {'type': 'VIEWED'});

    if (viewedResponse.statusCode == 200) {
      setState(() {
        _recentlyViewed.addAll(Api.parseList<Activity>(
            viewedResponse.bodyBytes, Activity.fromJson));
      });
    } else {
      throw Exception('Failed to load viewed products');
    }

    final purchasedResponse = await Api.get('/api/my_activity',
        queryParameters: {'type': 'PURCHASED'});

    if (purchasedResponse.statusCode == 200) {
      setState(() {
        _purchased.addAll(Api.parseList<Activity>(
            purchasedResponse.bodyBytes, Activity.fromJson));
      });
    } else {
      throw Exception('Failed to load purchased products');
    }
    setState(() {
      _loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: Padding(
          padding: const EdgeInsets.only(left: 8),
          child: IconButton(
            onPressed: () {
              Navigator.push(context,
                  MaterialPageRoute(builder: (context) => Settings()));
            },
            icon: Image.asset(
              "images/icons/cog.png",
              width: 24,
              height: 24,
            ),
            color: Colors.black,
          ),
        ),
        actions: [
          const NotificationsButton(),
        ],
      ),
      body: Conditional(
        condition: _profile != null,
        alternate: const Loading(loading: true),
        child: Column(
          children: [
            Container(
              color: const Color(0xFFFFC107),
              padding: EdgeInsetsDirectional.all(16),
              child: Center(
                child: Column(
                  children: [
                    Container(
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.all(Radius.circular(90)),
                        border:
                            Border.all(color: const Color(0xFFFFC107), width: 3),
                      ),
                      width: 120,
                      height: 120,
                      child: Container(
                        clipBehavior: Clip.hardEdge,
                        decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(90)),
                          border: Border.all(color: Colors.white, width: 1),
                          color: const Color(0xFFF5F5F5),
                        ),
                        child: Center(
                          child: _profile != null
                              ? ProfileAvatar(profile: _profile!, radius: 54)
                              : const SizedBox(),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 11,
                    ),
                    Text(
                      _profile?.displayName ?? '',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 24,
                              color: Colors.black,
                              fontWeight: FontWeight.w700)),
                    ),
                    const SizedBox(
                      height: 11,
                    ),
                    FollowersCounter(
                      profileId: _profile?.id,
                      onFollowersTap: () {
                        return widget.onFollowersTap(0);
                      },
                      onFollowingTap: () {
                        return widget.onFollowersTap(1);
                      },
                    )
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton(
                  onPressed: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const MyActivity()));
                  },
                  style: const ButtonStyle(
                      shape: MaterialStatePropertyAll(RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      )),
                      side: MaterialStatePropertyAll(
                          BorderSide(color: Color(0xFFD3D3D3), width: 1)),
                      minimumSize: MaterialStatePropertyAll(Size.zero),
                      padding: MaterialStatePropertyAll(
                          EdgeInsets.symmetric(horizontal: 20, vertical: 14))),
                  child: Row(
                    children: [
                      Image.asset(
                        "images/iconly/Light/Document.png",
                        width: 24,
                        height: 24,
                      ),
                      const SizedBox(
                        width: 8,
                      ),
                      Text(
                        'My activity',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontWeight: FontWeight.w600)),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  width: 16,
                ),
                OutlinedButton(
                  onPressed: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const Calendar()));
                  },
                  style: const ButtonStyle(
                      shape: MaterialStatePropertyAll(RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      )),
                      side: MaterialStatePropertyAll(
                          BorderSide(color: Color(0xFFD3D3D3), width: 1)),
                      minimumSize: MaterialStatePropertyAll(Size.zero),
                      padding: MaterialStatePropertyAll(
                          EdgeInsets.symmetric(horizontal: 25, vertical: 14))),
                  child: Row(
                    children: [
                      Image.asset(
                        "images/iconly/Light/Calendar.png",
                        width: 24,
                        height: 24,
                      ),
                      const SizedBox(
                        width: 8,
                      ),
                      Text(
                        'Calendar',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontWeight: FontWeight.w600)),
                      ),
                    ],
                  ),
                )
              ],
            ),
            const SizedBox(
              height: 22,
            ),
            const Divider(color: Color(0xFFF5F5F5), thickness: 10),
            const SizedBox(
              height: 8,
            ),
            Expanded(
                child: ListView(
              children: [
                const UpcomingEvents(),
                const SizedBox(
                  height: 26,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    'Recently viewed',
                    style: GoogleFonts.inter(
                        textStyle: const TextStyle(
                            fontSize: 16,
                            color: Colors.black,
                            fontWeight: FontWeight.w600)),
                  ),
                ),
                const SizedBox(
                  height: 16,
                ),
                Conditional(
                  condition: _recentlyViewed.isNotEmpty,
                  alternate: Padding(
                    padding: const EdgeInsets.only(top: 49),
                    child: TingsPlaceholder(
                      text: 'You don’t have any recent activity',
                      actionText: 'Explore Tings',
                      onActionTap: () {
                        Navigator.of(context).pushReplacement(MaterialPageRoute(
                            builder: (context) => const HomePage()));
                      },
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ProductsCarousel(
                          products:
                              _recentlyViewed.map((e) => e.product).toList()),
                      const SizedBox(
                        height: 26,
                      ),
                      Conditional(
                          condition: _purchased.isNotEmpty,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                child: Text(
                                  'Items purchased from a wishlist/registry',
                                  style: GoogleFonts.inter(
                                      textStyle: const TextStyle(
                                          fontSize: 16,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w600)),
                                ),
                              ),
                              const SizedBox(
                                height: 16,
                              ),
                              Conditional(
                                  condition: _purchased.isNotEmpty,
                                  child: ProductsCarousel(
                                      products: _purchased
                                          .map((e) => e.product)
                                          .toList())),
                            ],
                          )),
                      const SizedBox(
                        height: 50,
                      ),
                    ],
                  ),
                ),
              ],
            ))
          ],
        ),
      ),
    );
  }
}
