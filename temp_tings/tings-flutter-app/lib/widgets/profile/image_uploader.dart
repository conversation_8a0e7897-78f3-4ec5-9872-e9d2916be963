import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tings/globals.dart';
import 'package:http/http.dart';
import 'package:tings/widgets/shared/loading.dart';

class ImageUploader extends StatefulWidget {
  final Function(String) onChange;
  final String? url;

  const ImageUploader({super.key, required this.onChange, this.url});

  @override
  State<StatefulWidget> createState() => _ImageUploaderState();

}


class _ImageUploaderState extends State<ImageUploader> {
  final ImagePicker _picker = ImagePicker();
  bool uploading = false;

  void _pickImage() async {
    try {
      if (uploading != true) {
        final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
        if (image != null) {
          setState(() {
            uploading = true;
          });
          var uri = Uri(
            scheme: apiScheme,
            host: apiHost,
            port: apiPort,
            path: '/api/upload/AVATAR',
          );
          var request = MultipartRequest("POST", uri);
          request.files.add(await MultipartFile.fromPath(
            'file',
            image.path,
          ));
          var response = await request.send();
          if (response.statusCode == 200) {
            response.stream.transform(utf8.decoder).listen((value) async {
              setState(() {
                widget.onChange(jsonDecode(value)['cdnPath']);
              });
            });
          }
          setState(() {
            uploading = false;
          });
        }
      }
    } on Exception catch(e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please allow access to your photos in settings for the Tings app.', style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14, color: Colors.black))),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _pickImage,
      child: Container(
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(90)),
          border:
          Border.all(color: const Color(0xFFFFC107), width: 3),
        ),
        width: 120,
        height: 120,
        child: Container(
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            borderRadius:
            const BorderRadius.all(Radius.circular(90)),
            border: Border.all(color: Colors.white, width: 3),
            color: uploading == true ? Color(0xFFFFFFFF) : Color(0xFFF5F5F5),
          ),
          child: Center(
            child: uploading == true ? const Loading(loading: true) : widget.url != null
                ? buildAvatar()
                : buildSign(),
          ),
        ),
      ),
    );
  }

  Widget buildSign() {
    return Text('Add\nphoto',
        style: GoogleFonts.inter(
            textStyle: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
                decoration: TextDecoration.none)),
        textAlign: TextAlign.center);
  }

  Widget buildAvatar() {
    return CircleAvatar(
      minRadius: 135,
      backgroundImage:
      NetworkImage(widget.url!),
    );
  }
}