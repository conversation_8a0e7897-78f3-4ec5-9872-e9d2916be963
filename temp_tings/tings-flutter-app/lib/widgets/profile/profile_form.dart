import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart';
import 'package:tings/widgets/home.dart';
import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/profile/image_uploader.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/bottom_sheet_container.dart';
import 'package:tings/widgets/shared/conditional.dart';

import '../shared/async_text_form_field.dart';

class ProfileForm extends StatefulWidget {
  final bool? hideBackButton;
  final String? continueButtonLabel;
  final VoidCallback? onComplete;
  const ProfileForm(
      {super.key,
      this.hideBackButton,
      this.continueButtonLabel,
      this.onComplete});

  @override
  State<StatefulWidget> createState() => _ProfileFormState();
}

class _ProfileFormState extends State<ProfileForm> {
  Profile? profile;
  final _formKey = GlobalKey<FormState>();
  final Map<String, String> _values = {};
  final List<String> _days = [];
  final List<String> _months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ];
  final _usernameController = TextEditingController();
  final _nameController = TextEditingController();
  final _zipController = TextEditingController();

  bool _showErrorMessage = false;

  @override
  void initState() {
    for (num i = 1; i < 32; i++) {
      setState(() {
        _days.add(i.toString());
      });
    }
    init();
    super.initState();
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _nameController.dispose();
    _zipController.dispose();
    super.dispose();
  }

  void init() async {
    final response = await Api.get('/api/my_profile');
    final user = FirebaseAuth.instance.currentUser;

    if (response.statusCode == 200 && response.body != '') {
      setState(() {
        profile = Api.parse(response.bodyBytes, Profile.fromJson);
        _values['email'] = profile!.email;
        _values['displayName'] = profile!.displayName;
        _nameController.text = profile!.displayName;
        _values['username'] = profile!.username;
        _usernameController.text = profile!.username;
        if (profile!.birthMonth != null) {
          _values['birthMonth'] = _months[profile!.birthMonth! - 1];
        }
        if (profile!.birthDay != null) {
          _values['birthDay'] = profile!.birthDay.toString();
        }
        if (profile!.avatarUrl != null) {
          _values['avatarUrl'] = profile!.avatarUrl!;
        }
        if (profile!.zip != null) {
          _values['zip'] = profile!.zip!;
          _zipController.text = profile!.zip!;
        }
      });
    } else {
      setState(() {
        if (user?.email != null) {
          _values['email'] = user?.email as String;
          var username = user?.email?.split('@')[0] as String;
          _values['username'] = username;
          _usernameController.text = username;
        }
        if (user?.displayName != null) {
          _values['displayName'] = user?.displayName as String;
          _nameController.text = user?.displayName as String;
        }
        if (user?.photoURL != null) {
          _values['avatarUrl'] = user?.photoURL as String;
        }
      });
    }
  }

  Future<bool> checkUsername(String value) async {
    bool result = true;

    if (value.length < 3) {
      return false;
    }

    if (value != profile?.username) {
      var response = await Api.get(
          '/api/profile/verify', queryParameters: {'query': value});

      if (response.statusCode == 200) {
        result =
            Api.parse(response.bodyBytes, (json) => json['status'] == 'ok');
      } else if (response.statusCode == 400) {
        result = false;
      }
    }

    return result;
  }

  Future submit() async {
    if (_formKey.currentState!.validate()) {
      var newProfile = Profile.fromJson({
        'birthMonth': _values['birthMonth'] != null
            ? _months.indexOf(_values['birthMonth']!) + 1
            : profile?.birthMonth,
        'birthDay': _values['birthDay'] != null
            ? int.parse(_values['birthDay']!)
            : profile?.birthDay,
        'avatarUrl': _values['avatarUrl'] ?? profile?.avatarUrl,
        'displayName': _values['displayName'] ?? profile?.displayName,
        'username': _values['username'] ?? profile?.username,
        'email': _values['email'] ?? profile?.email,
        'zip': _values['zip'] ?? profile?.zip,
      });
      Response response;

      if (profile != null) {
        response = await Api.put('/api/profile', body: newProfile.toJson());
      } else {
        response = await Api.post('/api/profile', body: newProfile.toJson());
      }

      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Success')),
        );
        setState(() {
          profile = newProfile;
        });
        if (widget.onComplete != null) {
          widget.onComplete!();
        } else {
          Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const HomePage()),
              (r) => false);
        }
      } else {
        throw Exception('Failed to save profile');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'Profile',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.w700)),
            textAlign: TextAlign.center,
          ),
          centerTitle: true,
          leading: Conditional(
            condition: widget.hideBackButton != true,
            child: IconButton(
              icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
                  height: 24, width: 24),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
        ),
        body: Container(
          color: Colors.white,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  height: 15,
                ),
                ImageUploader(
                    onChange: (url) {
                      setState(() {
                        _values['avatarUrl'] = url;
                      });
                    },
                    url: _values['avatarUrl']),
                const SizedBox(
                  height: 15,
                ),
                Expanded(
                    child: Form(
                  key: _formKey,
                  child: ListView(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 24),
                    children: [
                      buildTextField(
                          required: true,
                          controller: _nameController,
                          errorMessage: '',
                          label: 'Name',
                          name: 'displayName'),
                      const SizedBox(
                        height: 16,
                      ),
                      AsyncTextFormField(
                        onChanged: (value) {
                          setState(() {
                            _values['username'] = value;
                          });
                        },
                        labelText: 'User name',
                        controller: _usernameController,
                        validationDebounce: const Duration(milliseconds: 500),
                        validator: checkUsername,
                        valueIsInvalidMessage: _values['username'] != null && _values['username']!.length < 3 ? 'Minimum length - 3 characters' : 'This username is not available',
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      buildTextField(
                          required: false,
                          controller: _zipController,
                          errorMessage: '',
                          label: 'Zipcode',
                          name: 'zip'),
                      const SizedBox(
                        height: 32,
                      ),
                      const InfoButton(),
                      const SizedBox(
                        height: 16,
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              validator: (value) {
                                /*if ((_values['birthDay'] != null &&
                                        _values['birthDay']!.isNotEmpty) &&
                                    (value == null || value.isEmpty)) {
                                  return 'Month is required';
                                }*/
                                if (value == null || value.isEmpty) {
                                  return 'Month is required';
                                }
                                return null;
                              },
                              hint: Text(
                                'Month',
                                style: GoogleFonts.inter(
                                    textStyle: const TextStyle(
                                        fontSize: 18,
                                        color: Colors.black,
                                        decoration: TextDecoration.none)),
                              ),
                              decoration: const InputDecoration(
                                hintText: '',
                                contentPadding: EdgeInsets.all(18),
                                border: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: const Color(0xFFCACACA),
                                        width: 1,
                                        style: BorderStyle.solid)),
                              ),
                              items: _months.map((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(value),
                                );
                              }).toList(),
                              value: _values['birthMonth'],
                              onChanged: (String? value) {
                                setState(() => _values['birthMonth'] = value!);
                              },
                            ),
                          ),
                          const SizedBox(
                            width: 16,
                          ),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              validator: (value) {
                                /*if ((_values['birthMonth'] != null &&
                                        _values['birthMonth']!.isNotEmpty) &&
                                    (value == null || value.isEmpty)) {
                                  return 'Day is required';
                                }*/
                                if (value == null || value.isEmpty) {
                                  return 'Day is required';
                                }
                                return null;
                              },
                              hint: Text(
                                'Day',
                                style: GoogleFonts.inter(
                                    textStyle: const TextStyle(
                                        fontSize: 18,
                                        color: Colors.black,
                                        decoration: TextDecoration.none)),
                              ),
                              decoration: const InputDecoration(
                                hintText: '',
                                contentPadding: EdgeInsets.all(18),
                                border: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: const Color(0xFFCACACA),
                                        width: 1,
                                        style: BorderStyle.solid)),
                              ),
                              items: _days.map((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(value),
                                );
                              }).toList(),
                              value: _values['birthDay'],
                              onChanged: (String? value) {
                                setState(() => _values['birthDay'] = value!);
                              },
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 32,
                        child: _showErrorMessage
                            ? Row(
                                children: [
                                  Image.asset(
                                    "images/iconly/Light/Info_Circle_red.png",
                                    height: 24,
                                    width: 24,
                                  ),
                                  const SizedBox(
                                    width: 4,
                                  ),
                                  Text('Please complete the missing fields',
                                      style: GoogleFonts.inter(
                                          textStyle: const TextStyle(
                                              fontSize: 16,
                                              color: Color(0xFFF64B4B),
                                              fontWeight: FontWeight.w400)),
                                      textAlign: TextAlign.left),
                                ],
                              )
                            : const SizedBox(),
                      ),
                    ],
                  ),
                )),
                Padding(
                    padding: const EdgeInsets.only(
                        top: 16, bottom: 30, left: 24, right: 24),
                    child: ElevatedButton(
                      onPressed: () {
                        submit();
                        /*if (_formKey.currentState!.validate()) {
                          submit();
                        } else {
                          setState(() {
                            _showErrorMessage = true;
                          });
                        }*/
                      },
                      style: const ButtonStyle(
                          padding: MaterialStatePropertyAll(
                              EdgeInsets.symmetric(
                                  horizontal: 35, vertical: 15))),
                      child: Text(
                        widget.continueButtonLabel ?? 'Continue',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                                fontSize: 18, fontWeight: FontWeight.w600)),
                      ),
                    )
                    /*child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        OutlinedButton(
                          onPressed: () {
                            Navigator.of(context).pushAndRemoveUntil(
                                MaterialPageRoute(
                                    builder: (context) => const HomePage()),
                                (r) => false);
                          },
                          style: const ButtonStyle(
                              minimumSize: MaterialStatePropertyAll(Size.zero),
                              padding: MaterialStatePropertyAll(
                                  EdgeInsets.symmetric(
                                      horizontal: 50, vertical: 15))),
                          child: Text(
                            'Later',
                            style: GoogleFonts.inter(
                                textStyle: const TextStyle(
                                    fontSize: 18,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600)),
                          ),
                        ),
                        const SizedBox(
                          width: 9,
                        ),
                        ElevatedButton(
                          onPressed: () {
                            if (_formKey.currentState!.validate()) {
                              submit();
                            } else {
                              setState(() {
                                _showErrorMessage = true;
                              });
                            }
                          },
                          style: const ButtonStyle(
                              minimumSize: MaterialStatePropertyAll(Size.zero),
                              padding: MaterialStatePropertyAll(
                                  EdgeInsets.symmetric(
                                      horizontal: 35, vertical: 15))),
                          child: Text(
                            'Continue',
                            style: GoogleFonts.inter(
                                textStyle: const TextStyle(
                                    fontSize: 18, fontWeight: FontWeight.w600)),
                          ),
                        ),
                      ],
                    )*/
                    )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildTextField(
      {required String errorMessage,
      required String label,
      required bool required,
      required String name,
      TextEditingController? controller,
      String? initialValue}) {
    return TextFormField(
      controller: controller,
      cursorColor: const Color(0xFFCACACA),
      initialValue: initialValue,
      validator: required
          ? (value) {
              if (value == null || value.isEmpty) {
                return errorMessage;
              }
              return null;
            }
          : null,
      onChanged: (value) {
        setState(() {
          _values[name] = value;
        });
      },
      style: GoogleFonts.inter(
          textStyle: const TextStyle(
              fontSize: 18,
              color: Colors.black,
              decoration: TextDecoration.none)),
      decoration: InputDecoration(
        labelText: label,
        errorStyle: const TextStyle(height: 0),
      ),
    );
  }
}

class InfoButton extends StatelessWidget {
  const InfoButton({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        showModalBottomSheet<void>(
          context: context,
          barrierColor: Colors.black87,
          builder: (BuildContext context) {
            return BottomSheetContainer(
              title: 'Why your birthday?',
              onClose: (val) {
                Navigator.pop(context);
              },
              height: 200,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 35),
                  child: Text(
                    'So your friends and family will be reminded of your big day and prompted to shower you with love!',
                    style: GoogleFonts.inter(
                        textStyle:
                            const TextStyle(fontSize: 18, color: Colors.black)),
                    textAlign: TextAlign.left,
                  ),
                )
              ],
            );
          },
        );
      },
      child: Row(
        children: [
          Text('When is your birthday?',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                      decoration: TextDecoration.none)),
              textAlign: TextAlign.left),
          Padding(
            padding: const EdgeInsets.only(top: 1, left: 3),
            child: Image.asset('images/iconly/Light/Info_Circle.png',
                height: 18, width: 18),
          ),
        ],
      ),
    );
  }
}
