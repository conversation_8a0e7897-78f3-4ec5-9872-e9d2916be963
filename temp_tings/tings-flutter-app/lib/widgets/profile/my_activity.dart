import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:tings/widgets/product/data/product.dart';
import 'package:tings/widgets/product/product_view.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/loading.dart';
import 'package:tings/widgets/shared/placeholder.dart';
import 'package:tings/widgets/shared/styled_tabs.dart';
import 'package:transparent_image/transparent_image.dart';

import '../friends/friend_card.dart';
import '../home.dart';
import '../shared/event_tracker.dart';
import '../shared/utils.dart';
import 'data/activity.dart';
import 'data/profile.dart';

class MyActivity extends StatefulWidget {
  const MyActivity({super.key});

  @override
  State<StatefulWidget> createState() => _MyActivityState();
}

class _MyActivityState extends State<MyActivity> {
  bool _loading = true;
  final List<Activity> _viewed = [];
  final List<Activity> _purchased = [];

  @override
  void initState() {
    getActivity();
    EventTracker().track('View My Activity');
    super.initState();
  }

  Future getActivity() async {
    setState(() {
      _loading = true;
    });

    final viewedResponse =
        await Api.get('/api/my_activity', queryParameters: {'type': 'VIEWED'});

    if (viewedResponse.statusCode == 200) {
      setState(() {
        _viewed.addAll(Api.parseList<Activity>(
            viewedResponse.bodyBytes, Activity.fromJson));
      });
    } else {
      throw Exception('Failed to load viewed products');
    }

    final purchasedResponse = await Api.get('/api/my_activity',
        queryParameters: {'type': 'PURCHASED'});

    if (purchasedResponse.statusCode == 200) {
      setState(() {
        _purchased.addAll(Api.parseList<Activity>(
            purchasedResponse.bodyBytes, Activity.fromJson));
      });
    } else {
      throw Exception('Failed to load purchased products');
    }

    setState(() {
      _loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'My activity',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        mainAxisAlignment:
            _loading ? MainAxisAlignment.center : MainAxisAlignment.start,
        children: [
          Expanded(
            child: Conditional(
              condition: _loading,
              alternate: Expanded(
                child: StyledTabs(
                  tabs: [
                    StyledTab(
                        title: 'Viewed',
                        widget: Conditional(
                          condition: _viewed.isNotEmpty,
                          alternate: TingsPlaceholder(
                            text: 'You don’t have any recent activity',
                            actionText: 'Explore Tings',
                            onActionTap: () {
                              Navigator.of(context).pushReplacement(
                                  MaterialPageRoute(
                                      builder: (context) => const HomePage()));
                            },
                          ),
                          child: ListView.separated(
                            shrinkWrap: true,
                            itemBuilder: (context, index) => _buildLine(
                                _viewed[index].product,
                                Text('Viewed on ${DateFormat('MMM d, yyyy').format(DateFormat("yyyy-MM-ddTHH:mm:ssZ").parseUTC(_viewed[index].createdAt).toLocal())}',
                                    style: const TextStyle(
                                        fontSize: 12, color: Color(0xB2000000)))
                                ),
                            separatorBuilder: (context, index) =>
                                const SizedBox(
                              height: 10,
                            ),
                            itemCount: _viewed.length,
                          ),
                        )),
                    StyledTab(
                        title: 'Purchased',
                        widget: Conditional(
                          condition: _purchased.isNotEmpty,
                          alternate: TingsPlaceholder(
                            text: 'You don’t have any recent activity',
                            actionText: 'Explore Tings',
                            onActionTap: () {
                              Navigator.of(context).pushReplacement(
                                  MaterialPageRoute(
                                      builder: (context) => const HomePage()));
                            },
                          ),
                          child: ListView.separated(
                            shrinkWrap: true,
                            itemBuilder: (context, index) => _buildLine(
                                _purchased[index].product,
                                _getPurchasedSubtitle(_purchased[index])),
                            separatorBuilder: (context, index) =>
                                const SizedBox(
                              height: 10,
                            ),
                            itemCount: _purchased.length,
                          ),
                        )),
                  ],
                ),
              ),
              child: Center(
                child: Loading(loading: _loading),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _getPurchasedSubtitle(Activity activity) {
    var date = DateFormat('MMM d, yyyy').format(
        DateFormat("yyyy-MM-ddTHH:mm:ssZ")
            .parseUTC(activity.createdAt)
            .toLocal());

    if (activity.wishListOwner != null) {
      return RichText(text: TextSpan(children: [
        const TextSpan(text: 'Purchased for '),
        TextSpan(
            text: activity.wishListOwner?.displayName,
            recognizer: TapGestureRecognizer()
              ..onTap = () => {
                if (activity.wishListOwner != null) {
                  Api.get('api/profile/${activity.wishListOwner!.id}').then((response) {
                    var profile = Api.parse<Profile>(response.bodyBytes, Profile.fromJson);
                    Navigator.of(context)
                        .push(MaterialPageRoute(
                        builder: (context) => FriendCard(profile: profile)));
                  })
                }
              },
            style: const TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold)
        ),
        TextSpan(text: ' on $date'),
      ], style: const TextStyle(fontSize: 14, color: Colors.black)));
    }

    return Text('Purchased on $date',
        style: const TextStyle(
            fontSize: 12, color: Color(0xB2000000)));
  }

  Widget _buildLine(Product product, Widget subtitle) {
    return Container(
      padding: const EdgeInsets.only(left: 20, right: 32),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(
            context,
            ProductView.routeName,
            arguments: ProductViewArguments(product: product),
          );
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 76,
              height: 76,
              clipBehavior: Clip.hardEdge,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(4)),
              ),
              child: FadeInImage.memoryNetwork(
                placeholder: kTransparentImage,
                image: product.thumbnail?.url ?? '',
                fit: BoxFit.cover,
                width: 76,
                height: 76,
              ),
            ),
            const SizedBox(
              width: 16,
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(product.title,
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                              fontWeight: FontWeight.w600)),
                      textAlign: TextAlign.left),
                  const SizedBox(
                    height: 2,
                  ),
                  Text('\$${Utils.convertToCurrency(product.price)}',
                      style: GoogleFonts.inter(
                          textStyle: const TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                              fontWeight: FontWeight.w600)),
                      textAlign: TextAlign.left),
                  const SizedBox(
                    height: 5,
                  ),
                  subtitle,
                ],
              ),
            ),
            Conditional(
                condition: product.inWishList == true,
                child: const SizedBox(height: 60, child: Icon(Icons.favorite))),
          ],
        ),
      ),
    );
  }
}
