import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tings/widgets/profile/data/friend_activity.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/friend_activity_line.dart';
import 'package:tings/widgets/shared/loading.dart';
import 'package:tings/widgets/shared/placeholder.dart';

import '../friends/friend_card.dart';
import '../shared/event_tracker.dart';
import 'data/profile.dart';

class Notifications extends StatefulWidget {
  const Notifications({super.key});

  @override
  State<StatefulWidget> createState() => _NotificationsState();
}

class _NotificationsState extends State<Notifications> {
  bool _loading = true;
  List<FriendActivity> _notifications = [];

  @override
  void initState() {
    EventTracker().track('View Notifications');
    init();
    super.initState();
  }

  void init() async {
    var prefs = await SharedPreferences.getInstance();
    var currentDate = '${DateTime.now().toUtc().toIso8601String().substring(0, 23)}Z';
    await prefs.setString('lastSeenNotificationDate', currentDate);
    getNotifications();
  }

  Future getNotifications() async {
    if (mounted) {
      setState(() {
        _loading = true;
      });

      var response = await Api.get('/api/notifications');
      if (response.statusCode == 200) {
        var results = Api.parseList<FriendActivity>(
            response.bodyBytes, FriendActivity.fromJson);
        setState(() {
          _notifications.clear();
          _notifications.addAll(results);
        });
      } else {
        throw Exception('Failed to search notifications');
      }

      setState(() {
        _loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Notifications',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        mainAxisAlignment:
            _loading ? MainAxisAlignment.center : MainAxisAlignment.start,
        children: [
          Expanded(
            child: Conditional(
              condition: _loading,
              alternate: Conditional(
                condition: _notifications.isNotEmpty,
                alternate: const TingsPlaceholder(
                  text: 'No notifications found',
                ),
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: ClampingScrollPhysics(),
                  itemBuilder: (context, index) =>
                      FriendActivityLine(
                        item: _notifications[index],
                        onTap: (String? profileId) async {
                          if (profileId != null) {
                            var response = await Api.get('api/profile/$profileId');
                            var profile = Api.parse<Profile>(response.bodyBytes, Profile.fromJson);
                            Navigator.of(context)
                                .push(MaterialPageRoute(
                                builder: (context) => FriendCard(profile: profile)));
                          }
                        }
                      ),
                  separatorBuilder: (context, index) => const SizedBox(
                    height: 10,
                  ),
                  itemCount: _notifications.length,
                ),
              ),
              child: const Loading(loading: true),
            ),
          ),
        ],
      ),
    );
  }
}
