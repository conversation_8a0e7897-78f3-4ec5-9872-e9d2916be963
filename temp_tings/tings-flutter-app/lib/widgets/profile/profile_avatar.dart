import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/profile/data/profile.dart';

class ProfileAvatar extends StatelessWidget {
  final Profile? profile;
  final String? avatarUrl;
  final String? displayName;
  final double? radius;
  final double? fontSize;
  final Color? color;
  final Color? labelColor;

  const ProfileAvatar(
      {super.key,
      this.profile,
      this.avatarUrl,
      this.displayName,
      this.radius,
      this.fontSize,
      this.labelColor,
      this.color});

  NetworkImage? getAvatarUrl() {
    if (profile?.avatarUrl != null) {
      return NetworkImage(profile!.avatarUrl!);
    }
    if (avatarUrl != null) {
      return NetworkImage(avatarUrl!);
    }
    return null;
  }

  String getDisplayName() {
    if (profile?.displayName != null) {
      return profile!.displayName;
    }
    if (displayName != null) {
      return displayName!;
    }

    return '';
  }

  String getInitials() {
    String displayName = getDisplayName();
    if (displayName.isNotEmpty) {
      return displayName
          .split(' ')
          .map((e) => e.substring(0, 1))
          .join('')
          .toUpperCase();
    } else {
      return displayName;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        color: color ?? const Color(0xFFDFDEDA),
        borderRadius: const BorderRadius.all(Radius.circular(200)),
      ),
      child: CircleAvatar(
        radius: radius,
        backgroundColor: color ?? const Color(0xFFDFDEDA),
        foregroundImage: getAvatarUrl(),
        child: getInitials().isNotEmpty
            ? Text(getInitials(),
                style: GoogleFonts.inter(
                    textStyle: TextStyle(
                        fontSize: fontSize ?? 18,
                        fontWeight: FontWeight.w500,
                        color: labelColor ?? Colors.black,
                        decoration: TextDecoration.none)),
                textAlign: TextAlign.center)
            : null,
      ),
    );
  }
}
