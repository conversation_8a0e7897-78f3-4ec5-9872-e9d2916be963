import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/profile/profile_form.dart';

import 'auth_buttons.dart';

class Auth extends StatelessWidget {
  const Auth({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Expanded(child: SizedBox()),
            Image.asset(
              "images/logo.png",
              width: 114,
              height: 67,
            ),
            const Expanded(child: SizedBox()),
            Text('Create Account/Sign in',
                style: GoogleFonts.inter(
                    textStyle: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: Colors.black,
                        decoration: TextDecoration.none)),
                textAlign: TextAlign.center),
            const SizedBox(
              height: 8,
            ),
            Padding(
              padding: const EdgeInsets.all(32),
              child: AuthButtons(onSuccess: () {
                Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const ProfileForm(
                              hideBackButton: true,
                            )));
              }),
            ),
            const Expanded(child: SizedBox()),
          ],
        ),
      ),
    );
  }
}
