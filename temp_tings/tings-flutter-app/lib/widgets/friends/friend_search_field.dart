import 'dart:async';

import 'package:flutter/material.dart';
import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/shared/api.dart';

import '../shared/loading.dart';

class FriendSearchField extends StatefulWidget {
  Function(List<Profile>?) onChange;

  FriendSearchField({super.key, required this.onChange});

  @override
  State<StatefulWidget> createState() => _FriendSearchFieldState();
}

class _FriendSearchFieldState extends State<FriendSearchField> {
  TextEditingController _controller = TextEditingController();
  bool _showClearButton = false;
  bool _isLoading = false;
  final _formKey = GlobalKey<FormState>();
  Timer? _debounce;

  List<Profile> searchResults = [];

  @override
  void initState() {
    _controller.addListener(() {
      setState(() {
        _showClearButton = _controller.text.isNotEmpty;
      });

      if (_debounce?.isActive ?? false) _debounce?.cancel();
      _debounce = Timer(const Duration(milliseconds: 500), () {
        if ((_controller.text.length > 2 && _formKey.currentState!.validate()) || _controller.text.isEmpty) {
          search(_controller.text);
        }
      });
    });
    super.initState();
  }

  void search(String term) async {
    if (mounted) {
      if (term.isEmpty) {
        setState(() {
          searchResults.clear();
          widget.onChange(null);
        });
      } else {
        setState(() {
          _isLoading = true;
        });
        var response = await Api.get('/api/profile/search',
            queryParameters: {'query': term});
        if (response.statusCode == 200) {
          var results =
              Api.parseList<Profile>(response.bodyBytes, Profile.fromJson);
          setState(() {
            searchResults.clear();
            searchResults.addAll(results);
            widget.onChange(results);
          });
        } else if (response.statusCode == 400) {
          setState(() {
            searchResults.clear();
            widget.onChange([]);
          });
        } else {
          throw Exception('Failed to search profiles');
        }
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: Stack(
              children: [
                Form(
                  key: _formKey,
                  child: TextFormField(
                    controller: _controller,
                    autofocus: false,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        RegExp regExp = RegExp(r'[^a-zA-Z0-9_\-\.]');
                        if (regExp.hasMatch(value)) {
                          return 'Invalid character in the username.';
                        }
                      }
                      return null;
                    },
                    decoration: const InputDecoration(
                      contentPadding: EdgeInsets.zero,
                      filled: true,
                      fillColor: Color(0xFFFAFAFA),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(70)),
                        borderSide: BorderSide(
                            color: Color(0xFFDDDDDD),
                            width: 1,
                            style: BorderStyle.solid),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(70)),
                        borderSide: BorderSide(
                            color: Color(0xFF484848),
                            width: 1,
                            style: BorderStyle.solid),
                      ),
                      labelText: 'Search...',
                      floatingLabelBehavior: FloatingLabelBehavior.never,
                      prefixIcon: ImageIcon(
                        color: Color(0xFF959595),
                        AssetImage("images/icons/Search_grey.png"),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  right: 0,
                  child: IconButton(
                      onPressed: () {
                        _controller.clear();
                        widget.onChange(null);
                      },
                      icon: Image.asset('images/icons/x-circle.png',
                          height: 22,
                          width: 22,
                          color: _showClearButton
                              ? Colors.black
                              : Colors.transparent)),
                ),
                Positioned(
                  right: 10,
                  top: 7,
                  width: 35,
                  height: 35,
                  child: Loading(
                    loading: _isLoading,
                    strokeWidth: 2,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
