import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tings/widgets/shared/loading.dart';

import '../profile/data/friend_activity.dart';
import '../profile/data/profile.dart';
import '../profile/proflie_list.dart';
import '../shared/api.dart';
import '../shared/conditional.dart';
import '../shared/friend_activity_line.dart';
import '../shared/placeholder.dart';
import '../shared/styled_tabs.dart';
import 'friend_card.dart';

class FriendsTabs extends StatelessWidget {
  List<Profile> following;
  List<Profile> followers;
  List<FriendActivity> activities;
  Function onRefresh;
  bool loading;
  int lastTabIndex;
  Function(int) onTabIndexChange;

  FriendsTabs({super.key,
    required this.activities,
    required this.following,
    required this.followers,
    required this.loading,
    required this.lastTabIndex,
    required this.onTabIndexChange,
    required this.onRefresh});

  @override
  Widget build(BuildContext context) {
    return StyledTabs(
      width: 370,
      initialActive: lastTabIndex,
      onTabChange: onTabIndexChange,
      tabs: [
        StyledTab(
            title: 'Followers',
            widget: Conditional(
              condition: followers.isNotEmpty,
              alternate: const TingsPlaceholder(
                text: 'You don’t have any followers yet',
              ),
              child: Conditional(
                condition: !loading,
                alternate: const Center(child: Loading(loading: true,)),
                child: ProfileList(
                  items: followers,
                  onTap: (profile) {
                    Navigator.of(context)
                        .push(MaterialPageRoute(
                        builder: (context) => FriendCard(profile: profile)))
                        .then((value) {
                      if (value == true) {
                        onRefresh();
                      }
                    });
                  },
                  activeButtonCondition: (profile) =>
                      following.any((element) => element.id == profile.id),
                  onActiveButtonPress: (profile) {
                    Api.post('/api/friend/${profile.id}')
                        .then((value) => onRefresh());
                  },
                  activeButtonWidth: 70,
                  activeButtonLabel: 'Follow',
                  onButtonPress: (profile) {
                    Api.delete('/api/friend/${profile.id}')
                        .then((value) => onRefresh());
                  },
                  buttonLabel: "Unfollow",
                  buttonWidth: 89,
                  avatarRadius: 28,
                  getTitle: (profile) => Text(profile.displayName,
                      style: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w600)),
                  getSubtitle: (profile) => Text(
                    'Birthday ${profile.birthMonth != null ? DateFormat('MMM d').format(DateTime(2022, profile.birthMonth!, profile.birthDay!)) : 'unknown'}',
                    style:
                    const TextStyle(fontSize: 12, color: Color(0xB2000000)),
                  ),
                ),
              ),
            )),
        StyledTab(
            title: 'Following',
            widget: Conditional(
              condition: following.isNotEmpty,
              alternate: const TingsPlaceholder(
                text: 'You don’t have any following yet',
              ),
              child: Conditional(
                condition: !loading,
                alternate: const Center(child: Loading(loading: true,)),
                child: ProfileList(
                  items: following,
                  onTap: (profile) {
                    Navigator.of(context)
                        .push(MaterialPageRoute(
                        builder: (context) => FriendCard(profile: profile)))
                        .then((value) {
                          if (value == true) {
                            onRefresh();
                          }
                        });
                  },
                  activeButtonCondition: (profile) {
                    return true;
                  },
                  onButtonPress: (profile) {
                    Api.delete('/api/friend/${profile.id}')
                        .then((value) => onRefresh());
                  },
                  buttonLabel: "Unfollow",
                  buttonWidth: 89,
                  avatarRadius: 28,
                  getTitle: (profile) => Text(profile.displayName,
                      style: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w600)),
                  getSubtitle: (profile) => Text(
                    'Birthday ${profile.birthMonth != null ? DateFormat('MMM d').format(DateTime(2022, profile.birthMonth!, profile.birthDay!)) : 'unknown'}',
                    style:
                    const TextStyle(fontSize: 12, color: Color(0xB2000000)),
                  ),
                ),
              ),
            )),
        StyledTab(
            title: 'Activity',
            widget: Conditional(
              condition: activities.isNotEmpty,
              alternate: const TingsPlaceholder(
                text:
                'Your friends have not yet viewed/purchased any items',
              ),
              child: ListView.separated(
                shrinkWrap: true,
                physics: ClampingScrollPhysics(),
                itemBuilder: (context, index) =>
                    FriendActivityLine(item: activities[index], onTap: (String? profileId) async {
                      if (profileId != null) {
                        var response = await Api.get('api/profile/$profileId');
                        var profile = Api.parse<Profile>(response.bodyBytes, Profile.fromJson);
                        Navigator.of(context)
                            .push(MaterialPageRoute(
                            builder: (context) => FriendCard(profile: profile)));
                      }
                    },),
                separatorBuilder: (context, index) => const SizedBox(
                  height: 10,
                ),
                itemCount: activities.length,
              ),
            )),
      ],
    );
  }

}