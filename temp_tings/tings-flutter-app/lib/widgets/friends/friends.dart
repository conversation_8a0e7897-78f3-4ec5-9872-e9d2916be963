import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tings/widgets/friends/friends_info.dart';
import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/loading.dart';
import 'package:tings/widgets/shared/placeholder.dart';

import '../profile/data/friend_activity.dart';
import '../profile/proflie_list.dart';
import '../shared/event_tracker.dart';
import 'friend_search_field.dart';
import 'friends_tabs.dart';

class Friends extends StatefulWidget {
  final int? startIndex;
  const Friends({super.key, this.startIndex});

  @override
  State<StatefulWidget> createState() => _FriendsState();
}

class _FriendsState extends State<Friends> {
  bool _loading = true;
  List<Profile> _following = [];
  List<Profile> _followers = [];
  List<FriendActivity> _activities = [];
  List<Profile>? _searchResults;
  late int lastTabIndex;

  @override
  void initState() {
    init();
    EventTracker().track('View Connect');
    super.initState();
  }

  void init() async {
    var prefs = await SharedPreferences.getInstance();
    var friendsInfoApproved = prefs.getBool('friendsInfoApproved');

    if (widget.startIndex != null) {
      setState(() {
        lastTabIndex = widget.startIndex!;
      });
    } else {
      lastTabIndex = 0;
    }

    getFriends();
    getActivities();

    if (friendsInfoApproved != true) {
      showInfoDialog();
    }

    await prefs.setBool('friendsInfoApproved', true);
  }

  void showInfoDialog() {
    showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
            scrollable: true,
            content: FriendsInfo(onClose: (){
              Navigator.of(context).pop();
            },)
        )
    );
  }

  Future getActivities() async {
    if (mounted) {
      var response = await Api.get('/api/friend_activity');
      if (response.statusCode == 200) {
        var results = Api.parseList<FriendActivity>(
            response.bodyBytes, FriendActivity.fromJson);
        setState(() {
          _activities.clear();
          _activities.addAll(results);
        });
      } else {
        throw Exception('Failed to search activities');
      }
    }
  }

  Future getFriends() async {
    if (mounted) {
      setState(() {
        _loading = true;
      });

      var response = await Api.get('/api/following');
      if (response.statusCode == 200) {
        var results =
            Api.parseList<Profile>(response.bodyBytes, Profile.fromJson);
        setState(() {
          _following.clear();
          _following.addAll(results);
        });
      } else if (response.statusCode == 400) {
      } else {
        throw Exception('Failed to get following');
      }

      var responseFollowers = await Api.get('/api/followers');
      if (responseFollowers.statusCode == 200) {
        var results =
            Api.parseList<Profile>(responseFollowers.bodyBytes, Profile.fromJson);
        setState(() {
          _followers.clear();
          _followers.addAll(results);
        });
      } else if (response.statusCode == 400) {
      } else {
        throw Exception('Failed to get followers');
      }

      setState(() {
        _loading = false;
      });
    }
  }

  void onSearchChange(List<Profile>? results) {
    setState(() {
      _searchResults = results;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Connect',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(32),
          child: FriendSearchField(onChange: onSearchChange)
        ),
      ),
      body: Conditional(
        condition: _searchResults == null,
        alternate: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Conditional(
            condition: _searchResults != null && _searchResults!.isNotEmpty,
            alternate: const TingsPlaceholder(
              text: 'No results found',
            ),
            child: ProfileList(
              items: _searchResults ?? [],
              activeButtonLabel: 'Follow',
              buttonLabel: 'Unfollow',
              activeButtonWidth: 70,
              buttonWidth: 84,
              activeButtonCondition: (profile) =>
                  _following.any((element) => element.id == profile.id),
              onActiveButtonPress: (profile) {
                setState(() {
                  _following.add(profile);
                });
                Api.post('/api/friend/${profile.id}');
              },
              onButtonPress: (profile) {
                setState(() {
                  _following
                      .removeWhere((element) => element.id == profile.id);
                });
                Api.delete('/api/friend/${profile.id}');
              },
              onTap: (profile) {
                setState(() {
                  if (_following
                      .any((element) => element.id == profile.id)) {
                    _following
                        .removeWhere((element) => element.id == profile.id);
                    Api.delete('/api/friend/${profile.id}');
                  } else {
                    _following.add(profile);
                    Api.post('/api/friend/${profile.id}');
                  }
                });
              },
            ),
          )
        ),
        child: FriendsTabs(
          lastTabIndex: lastTabIndex,
          activities: _activities,
          following: _following,
          followers: _followers,
          onRefresh: getFriends,
          loading: _loading,
          onTabIndexChange: (value) {
            setState(() {
              lastTabIndex = value;
            });
          },
        ),
      ),
    );
  }
}
