import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/profile/proflie_list.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/conditional.dart';

class AddFriendsArguments {
  final List<Profile> friends;

  AddFriendsArguments(this.friends);
}

class AddFriends extends StatefulWidget {
  final List<Profile> friends;
  static const String routeName = '/add-friends';

  const AddFriends({super.key, required this.friends});

  @override
  State<StatefulWidget> createState() => _AddFriendsState();
}

class _AddFriendsState extends State<AddFriends> {
  TextEditingController _controller = TextEditingController();
  bool _showClearButton = false;
  bool _isLoading = false;
  FocusNode? _focusNode;
  final _formKey = GlobalKey<FormState>();
  Timer? _debounce;

  late List<Profile> existingFriends;
  List<Profile> searchResults = [];

  @override
  void initState() {
    setState(() {
      existingFriends = widget.friends;
    });
    _controller.addListener(() {
      setState(() {
        _showClearButton = _controller.text.isNotEmpty;
      });

      if (_debounce?.isActive ?? false) _debounce?.cancel();
      _debounce = Timer(const Duration(milliseconds: 500), () {
        if ((_controller.text.length > 2 && _formKey.currentState!.validate()) || _controller.text.isEmpty) {
          search(_controller.text);
        }
      });
    });
    super.initState();
  }

  void search(String term) async {
    if (mounted) {
      if (term.isEmpty) {
        setState(() {
          searchResults.clear();
          searchResults.addAll(existingFriends);
        });
      } else {
        var response = await Api.get('/api/profile/search',
            queryParameters: {'query': term});
        if (response.statusCode == 200) {
          var results =
              Api.parseList<Profile>(response.bodyBytes, Profile.fromJson);
          setState(() {
            searchResults.clear();
            searchResults.addAll(existingFriends.where((element) => element
                .displayName
                .toLowerCase()
                .contains(term.toLowerCase())));
            searchResults.addAll(results.where((element) =>
                !existingFriends.any((exst) => element.id == exst.id)));
          });
        } else if (response.statusCode == 400) {
          setState(() {
            searchResults.clear();
            searchResults.addAll(existingFriends.where((element) => element
                .displayName
                .toLowerCase()
                .contains(term.toLowerCase())));
          });
        } else {
          throw Exception('Failed to search profiles');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Add following',
          style: GoogleFonts.inter(
              textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w700)),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Form(
                        key: _formKey,
                        child: TextFormField(
                          controller: _controller,
                          autofocus: true,
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              RegExp regExp = RegExp(r'[^a-zA-Z0-9_\-\.]');
                              if (regExp.hasMatch(value)) {
                                return 'Invalid character in the username.';
                              }
                            }
                            return null;
                          },
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.zero,
                            filled: true,
                            fillColor: Color(0xFFFAFAFA),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.all(Radius.circular(70)),
                              borderSide: BorderSide(
                                  color: Color(0xFFDDDDDD),
                                  width: 1,
                                  style: BorderStyle.solid),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.all(Radius.circular(70)),
                              borderSide: BorderSide(
                                  color: Color(0xFF484848),
                                  width: 1,
                                  style: BorderStyle.solid),
                            ),
                            labelText: 'Search...',
                            floatingLabelBehavior: FloatingLabelBehavior.never,
                            prefixIcon: ImageIcon(
                              color: Color(0xFF959595),
                              AssetImage("images/icons/Search_grey.png"),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        right: 0,
                        child: IconButton(
                            onPressed: () {
                              _controller.clear();
                            },
                            icon: Image.asset('images/icons/x-circle.png',
                                height: 22,
                                width: 22,
                                color: _showClearButton
                                    ? Colors.black
                                    : Colors.transparent)),
                      ),
                      Positioned(
                        right: 55,
                        top: 10,
                        child: Conditional(
                          condition: _isLoading,
                          child: const SizedBox(
                            height: 25,
                            width: 25,
                            child: CircularProgressIndicator(strokeWidth: 3),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                /*Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: TextButton(
                    style: TextButton.styleFrom(
                      textStyle: const TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                          decoration: TextDecoration.underline),
                    ),
                    onPressed: () {
                      _focusNode?.unfocus();
                      _controller.clear();
                      Navigator.of(context).pop(true);
                    },
                    child: const Text('Invite',
                        style: TextStyle(
                            fontSize: 16,
                            color: Colors.black,
                            decoration: TextDecoration.underline)),
                  ),
                ),*/
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: ProfileList(
                items: searchResults,
                activeButtonLabel: 'Follow',
                buttonLabel: 'Unfollow',
                activeButtonWidth: 70,
                buttonWidth: 84,
                activeButtonCondition: (profile) =>
                    existingFriends.any((element) => element.id == profile.id),
                onActiveButtonPress: (profile) {
                  setState(() {
                    existingFriends.add(profile);
                  });
                  Api.post('/api/friend/${profile.id}');
                },
                onButtonPress: (profile) {
                  setState(() {
                    existingFriends
                        .removeWhere((element) => element.id == profile.id);
                  });
                  Api.delete('/api/friend/${profile.id}');
                },
                onTap: (profile) {
                  setState(() {
                    if (existingFriends
                        .any((element) => element.id == profile.id)) {
                      existingFriends
                          .removeWhere((element) => element.id == profile.id);
                      Api.delete('/api/friend/${profile.id}');
                    } else {
                      existingFriends.add(profile);
                      Api.post('/api/friend/${profile.id}');
                    }
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
