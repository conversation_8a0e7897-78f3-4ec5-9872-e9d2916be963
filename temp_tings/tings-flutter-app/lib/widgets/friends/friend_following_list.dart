import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/loading.dart';

import '../profile/data/profile.dart';
import '../shared/api.dart';
import '../shared/event_tracker.dart';
import 'friend_profile_list.dart';

class FriendFollowingListArguments {
  final Profile profile;

  FriendFollowingListArguments(this.profile);
}

class FriendFollowingList extends StatefulWidget {
  final Profile profile;

  static const String routeName = '/friend-following';

  const FriendFollowingList({
    super.key,
    required this.profile
  });

  @override
  State<StatefulWidget> createState() => _FriendFollowingListState();
}

class _FriendFollowingListState extends State<FriendFollowingList> {
  List<Profile> _following = [];
  List<Profile> _myFollowing = [];
  String? profileId;
  bool _loading = true;

  @override
  void initState() {
    EventTracker().track('View Other Profiles following');
    init();
    super.initState();
  }

  Future init() async {
    setState(() {
      _loading = true;
    });
    await getMyProfileId();
    await getMyFollowing();
    await getFollowing();
    setState(() {
      _loading = false;
    });
  }

  Future getMyProfileId() async {
    if (mounted) {
      final response = await Api.get('/api/my_profile');
      if (response.statusCode == 200 && response.body != '') {
        var profile = Api.parse(response.bodyBytes, Profile.fromJson);
        setState(() {
          profileId = profile.id;
        });
      } else {
        throw Exception('Failed to load profile');
      }
    }
  }

  Future getMyFollowing() async {
    if (mounted) {
      var response = await Api.get('/api/following');
      if (response.statusCode == 200) {
        var results =
        Api.parseList<Profile>(response.bodyBytes, Profile.fromJson);
        setState(() {
          _myFollowing.clear();
          _myFollowing.addAll(results);
        });
      } else if (response.statusCode == 400) {
      } else {
        throw Exception('Failed to get following');
      }
    }
  }

  Future getFollowing() async {
    if (mounted) {
      var response = await Api.get('/api/following/${widget.profile.id}');
      if (response.statusCode == 200) {
        var results =
        Api.parseList<Profile>(response.bodyBytes, Profile.fromJson);
        setState(() {
          _following.clear();
          _following.addAll(results);
        });
      } else if (response.statusCode == 400) {
      } else {
        throw Exception('Failed to get following');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              '${widget.profile.displayName} Following',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 20,
                      color: Colors.black,
                      fontWeight: FontWeight.w700)),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        centerTitle: true,
      ),
      body: Conditional(
        condition: !_loading,
        alternate: const Center(
          child: Loading(loading: true,),
        ),
        child: FriendProfileList(
          following: _myFollowing,
          profiles: _following,
          onRefresh: getMyFollowing,
          myProfileId: profileId,
        ),
      ),
    );
  }

}