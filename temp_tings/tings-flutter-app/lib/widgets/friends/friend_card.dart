import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:tings/widgets/friends/data/followers_count.dart';
import 'package:tings/widgets/friends/friend_followers_list.dart';
import 'package:tings/widgets/friends/friend_following_list.dart';
import 'package:tings/widgets/profile/data/profile.dart';
import 'package:tings/widgets/profile/profile_avatar.dart';
import 'package:tings/widgets/shared/api.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/followers_counter.dart';
import 'package:tings/widgets/wishlist/data/wishlist.dart';
import 'package:tings/widgets/wishlist/widgets/wishlist_list.dart';
import 'package:tings/widgets/wishlist/wishlist_data_provider.dart';
import 'package:tings/widgets/wishlist/wishlist_items_list.dart';

import '../shared/event_tracker.dart';

class FriendCard extends StatefulWidget {
  final Profile profile;

  const FriendCard({super.key, required this.profile});

  @override
  State<StatefulWidget> createState() => _FriendCardState();
}

class _FriendCardState extends State<FriendCard> {
  bool? isFollowing;
  bool shouldRefresh = false;
  List<Wishlist> _wishlists = [];

  @override
  void initState() {
    EventTracker().track('View Other Profiles');
    getWishlists();
    getIsFollowing();
    super.initState();
  }

  Future getIsFollowing() async {
    var res = await Api.get('/api/is_following/${widget.profile.id}');
    setState(() {
      isFollowing = Api.parse(res.bodyBytes, (json) {
        return json['isFollowing'];
      });
    });
  }

  Future getWishlists() async {
    var wishlists = await WishlistDataProvider.getWishlistList(
        clientProfileId: widget.profile.id);
    setState(() {
      _wishlists.clear();
      _wishlists.addAll(wishlists);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: MediaQuery.of(context).padding,
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(fit: StackFit.passthrough, children: [
                Center(
                  child: Column(
                    children: [
                      Container(
                        clipBehavior: Clip.hardEdge,
                        decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(90)),
                          border: Border.all(
                              color: const Color(0xFFFFC107), width: 3),
                        ),
                        width: 120,
                        height: 120,
                        child: Container(
                            clipBehavior: Clip.hardEdge,
                            decoration: BoxDecoration(
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(90)),
                              border: Border.all(color: Colors.white, width: 3),
                              color: const Color(0xFFF5F5F5),
                            ),
                            child: ProfileAvatar(
                              profile: widget.profile,
                              radius: 56,
                            )),
                      ),
                      const SizedBox(
                        height: 11,
                      ),
                      Text(
                        widget.profile.displayName,
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                                fontSize: 24,
                                color: Colors.black,
                                fontWeight: FontWeight.w700)),
                      ),
                      const SizedBox(
                        height: 11,
                      ),
                      Text(
                        'Birthday ${widget.profile.birthMonth != null ? DateFormat('MMM d').format(DateTime(2022, widget.profile.birthMonth!, widget.profile.birthDay!)) : 'unknown'}',
                        style: GoogleFonts.inter(
                            textStyle: const TextStyle(
                          fontSize: 14,
                          color: Colors.black,
                        )),
                      ),
                      const SizedBox(
                        height: 14,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          /*SizedBox(
                            width: 137,
                            height: 31,
                            child: OutlinedButton(
                              onPressed: () {},
                              style: const ButtonStyle(
                                  padding: MaterialStatePropertyAll(
                                      EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 8))),
                              child: Text(
                                'Send a Quick Ting',
                                style: GoogleFonts.inter(
                                    textStyle: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w600)),
                              ),
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),*/
                          FollowButton(),
                        ],
                      ),
                      FollowersCounter(
                        profileId: widget.profile.id,
                        onFollowersTap: () {
                          return Navigator.pushNamed(
                            context,
                            FriendFollowersList.routeName,
                            arguments: FriendFollowersListArguments(
                                widget.profile
                            ),
                          );
                        },
                        onFollowingTap: () {
                          return Navigator.pushNamed(
                            context,
                            FriendFollowingList.routeName,
                            arguments: FriendFollowingListArguments(
                                widget.profile
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                Positioned(
                  // red box
                  right: 16,
                  top: 0,
                  child: IconButton(
                    onPressed: () => Navigator.of(context).pop(shouldRefresh),
                    icon: Image.asset(
                      "images/icons/clear.png",
                      width: 18,
                      height: 18,
                    ),
                    color: Colors.black,
                  ),
                )
              ]),
              const SizedBox(
                height: 25,
              ),
              const Divider(color: Color(0xFFF5F5F5), thickness: 10),
              const SizedBox(
                height: 14,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  '${widget.profile.displayName}’s lists',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 14,
                          color: Colors.black,
                          fontWeight: FontWeight.w600)),
                ),
              ),
              Expanded(
                  child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: WishlistList(
                  list: _wishlists,
                  onClick: (wishlist) {
                    Navigator.pushNamed(
                      context,
                      WishlistItemsList.routeName,
                      arguments: WishlistItemsListArguments(wishlist, false),
                    ).then((value) async {
                      if (value == true) {
                        await getWishlists();
                      }
                    });
                  },
                ),
              ))
            ],
          ),
        ),
      ),
    );
  }

  Widget FollowButton() {
    return SizedBox(
      width: 84,
      height: 31,
      child: Conditional(
        condition: isFollowing != null,
        child: Conditional(
          condition: isFollowing == true,
          alternate: ElevatedButton(
            onPressed: () {
              Api.post('/api/friend/${widget.profile.id}')
                  .then((value) {
                getIsFollowing();
                setState(() {
                  shouldRefresh = true;
                });
              });
            },
            style: const ButtonStyle(
                padding: MaterialStatePropertyAll(
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8))),
            child: Text(
              'Follow',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w600)),
            ),
          ),
          child: OutlinedButton(
            onPressed: () {
              Api.delete('/api/friend/${widget.profile.id}')
                  .then((value) =>
                  Navigator.of(context).pop(true));
            },
            style: const ButtonStyle(
                padding: MaterialStatePropertyAll(
                    EdgeInsets.symmetric(
                        horizontal: 16, vertical: 8))),
            child: Text(
              'Unfollow',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 12,
                      color: Colors.black,
                      fontWeight: FontWeight.w600)),
            ),
          ),
        ),
      ),
    );
  }
}
