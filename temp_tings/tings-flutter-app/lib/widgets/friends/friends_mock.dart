List<Map<String, dynamic>> friends = [
  {
    "id": "64b0cdd5-aaef-4fd2-aaac-d91d00ea9e60",
    "birthMonth": 6,
    "birthDay": 24,
    "displayName": "<PERSON><PERSON>",
    "username": "<PERSON><PERSON><PERSON>",
    "email": "r<PERSON><PERSON><PERSON>@zboo.com"
  },
  {
    "id": "df8e47d4-2afd-41c5-8333-47440e310757",
    "birthMonth": 4,
    "birthDay": 18,
    "displayName": "<PERSON>",
    "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    "email": "<EMAIL>"
  },
  {
    "id": "72eb2ce7-fa24-4355-af8a-7ee536cfa3e1",
    "birthMonth": 1,
    "birthDay": 4,
    "displayName": "<PERSON>",
    "username": "<PERSON><PERSON><PERSON><PERSON>",
    "email": "<EMAIL>"
  },
  {
    "id": "eb77ec03-1c34-4caf-910c-0bd14e9ff1de",
    "birthMonth": 11,
    "birthDay": 14,
    "displayName": "<PERSON><PERSON><PERSON>",
    "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "email": "whita<PERSON><PERSON><PERSON>@zboo.com"
  },
  {
    "id": "d4a80eb9-f2dd-44fc-8b5d-274d68f5df9f",
    "birthMonth": 7,
    "birthDay": 13,
    "displayName": "Calhoun Mclean",
    "username": "Mindy_<PERSON>",
    "email": "<EMAIL>"
  },
  {
    "id": "0c4637a0-7d31-424a-aa5d-f7f98bf84568",
    "birthMonth": 9,
    "birthDay": 16,
    "displayName": "Taylor Emerson",
    "username": "Whitley_Bishop",
    "email": "<EMAIL>"
  },
  {
    "id": "281bf6f5-4409-4481-86c5-45b82fd31b65",
    "birthMonth": 2,
    "birthDay": 6,
    "displayName": "Amie Lawrence",
    "username": "Horne_Wooten",
    "email": "<EMAIL>"
  },
  {
    "id": "7128f4b4-a852-49df-9b34-5863f24e012e",
    "birthMonth": 2,
    "birthDay": 10,
    "displayName": "Cheri Holloway",
    "username": "Stark_Black",
    "email": "<EMAIL>"
  },
  {
    "id": "18c83e4b-924e-4e28-84a5-dc888b45c93d",
    "birthMonth": 4,
    "birthDay": 30,
    "displayName": "Dalton Noble",
    "username": "Millie_Randall",
    "email": "<EMAIL>"
  },
  {
    "id": "11bcfe9a-b8d7-4c78-a983-eac56218097c",
    "birthMonth": 1,
    "birthDay": 17,
    "displayName": "Spencer Moore",
    "username": "Jacobs_Maldonado",
    "email": "<EMAIL>"
  },
  {
    "id": "44fcdff0-242c-4e51-939f-a8a754f15dfc",
    "birthMonth": 4,
    "birthDay": 20,
    "displayName": "Mara Juarez",
    "username": "Lela_Daniel",
    "email": "<EMAIL>"
  },
  {
    "id": "3aa9c54d-1d65-4867-bf79-6f8abc23f9de",
    "birthMonth": 7,
    "birthDay": 8,
    "displayName": "Hodge Mcbride",
    "username": "Rosemarie_Walsh",
    "email": "<EMAIL>"
  },
  {
    "id": "88fb898d-10c8-43dd-82d3-b73065e5c014",
    "birthMonth": 2,
    "birthDay": 2,
    "displayName": "Guadalupe Hoover",
    "username": "Amy_Gregory",
    "email": "<EMAIL>"
  },
  {
    "id": "988b26df-129d-43c1-8ad3-245b16925a77",
    "birthMonth": 4,
    "birthDay": 3,
    "displayName": "Bird Copeland",
    "username": "Vicky_Pratt",
    "email": "<EMAIL>"
  },
  {
    "id": "a8d3eb69-a26f-44c3-955a-4c49f39e1907",
    "birthMonth": 8,
    "birthDay": 8,
    "displayName": "Luna Fletcher",
    "username": "Perry_Nguyen",
    "email": "<EMAIL>"
  },
  {
    "id": "34fa79bf-078a-42fc-b3b1-410711df835b",
    "birthMonth": 6,
    "birthDay": 25,
    "displayName": "Ashlee Guy",
    "username": "Alford_Baird",
    "email": "<EMAIL>"
  },
  {
    "id": "820553ce-d913-4ab1-bebc-d56a18192ed6",
    "birthMonth": 7,
    "birthDay": 19,
    "displayName": "Rochelle Craft",
    "username": "Daugherty_Barnes",
    "email": "<EMAIL>"
  },
  {
    "id": "0a5e2392-5292-4899-ab9f-e357af51b200",
    "birthMonth": 6,
    "birthDay": 26,
    "displayName": "Myrna Gomez",
    "username": "Harvey_Michael",
    "email": "<EMAIL>"
  },
  {
    "id": "f6e86568-0fbc-46a3-b6f0-ad6ee36c9be1",
    "birthMonth": 11,
    "birthDay": 30,
    "displayName": "Wallace Roy",
    "username": "Yvette_Delaney",
    "email": "<EMAIL>"
  },
  {
    "id": "df87c5a5-8860-4a78-9236-d21d942e462e",
    "birthMonth": 4,
    "birthDay": 16,
    "displayName": "Blanche Mcmahon",
    "username": "Edwina_Cameron",
    "email": "<EMAIL>"
  },
  {
    "id": "609a9b1a-717f-49a8-9014-3dd35e1ddb50",
    "birthMonth": 9,
    "birthDay": 23,
    "displayName": "Regina David",
    "username": "Sandra_Hahn",
    "email": "<EMAIL>"
  },
  {
    "id": "5f3383a5-73b3-4995-a178-51915b7da619",
    "birthMonth": 9,
    "birthDay": 6,
    "displayName": "Miranda Dudley",
    "username": "Morrison_Spencer",
    "email": "<EMAIL>"
  },
  {
    "id": "29828713-6e4a-4fa7-bc21-a7487a5768f4",
    "birthMonth": 6,
    "birthDay": 21,
    "displayName": "Solomon Livingston",
    "username": "Autumn_Kane",
    "email": "<EMAIL>"
  },
  {
    "id": "411c3e96-30ae-4e94-a399-06377f001f3d",
    "birthMonth": 5,
    "birthDay": 4,
    "displayName": "Vazquez Wilkins",
    "username": "Wendi_Walls",
    "email": "<EMAIL>"
  },
  {
    "id": "ca10ec10-aea5-4665-bd28-98a39cc76b36",
    "birthMonth": 2,
    "birthDay": 17,
    "displayName": "Burns Giles",
    "username": "Simon_Booth",
    "email": "<EMAIL>"
  },
  {
    "id": "1e5bf24c-7c34-4e4e-a143-6ce40c1ff6ac",
    "birthMonth": 5,
    "birthDay": 16,
    "displayName": "Slater Hyde",
    "username": "Michael_Monroe",
    "email": "<EMAIL>"
  },
  {
    "id": "4d46bfec-7c24-4cec-84a5-58f34ab9436a",
    "birthMonth": 7,
    "birthDay": 1,
    "displayName": "Kasey Stone",
    "username": "Pollard_Gould",
    "email": "<EMAIL>"
  },
  {
    "id": "0c03ce32-070d-40f1-98b0-b212f74b49ef",
    "birthMonth": 3,
    "birthDay": 29,
    "displayName": "Webster Jacobson",
    "username": "Hernandez_Swanson",
    "email": "<EMAIL>"
  },
  {
    "id": "1d743f2f-88cc-4192-9901-7d2d3a6aa7b3",
    "birthMonth": 2,
    "birthDay": 12,
    "displayName": "Thompson Oneil",
    "username": "Kelly_Head",
    "email": "<EMAIL>"
  }
];
