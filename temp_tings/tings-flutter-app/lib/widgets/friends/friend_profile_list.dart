import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../profile/data/profile.dart';
import '../profile/proflie_list.dart';
import '../shared/api.dart';
import 'friend_card.dart';

class FriendProfileList extends StatelessWidget {
  final String? myProfileId;
  final List<Profile> profiles;
  final List<Profile> following;
  final Function? onRefresh;


  const FriendProfileList({
    super.key,
    this.myProfileId,
    this.onRefresh,
    required this.following,
    required this.profiles
  });

  @override
  Widget build(BuildContext context) {
    return ProfileList(
      items: profiles,
      onTap: (profile) {
        Navigator.of(context)
            .push(MaterialPageRoute(
            builder: (context) => FriendCard(profile: profile)))
            .then((value) {
          if (value == true && onRefresh != null) {
            onRefresh!();
          }
        });
      },
      showButton: (profile) => profile.id != myProfileId,
      activeButtonCondition: (profile) =>
           following.any((element) => element.id == profile.id),
      onActiveButtonPress: (profile) {
        Api.post('/api/friend/${profile.id}')
            .then((value) {
          if (onRefresh != null) {
            onRefresh!();
          }
        });
      },
      activeButtonWidth: 70,
      activeButtonLabel: 'Follow',
      onButtonPress: (profile) {
        Api.delete('/api/friend/${profile.id}')
            .then((value) {
          if (onRefresh != null) {
            onRefresh!();
          }
        });
      },
      buttonLabel: "Unfollow",
      buttonWidth: 89,
      avatarRadius: 28,
      getTitle: (profile) => Text(profile.displayName,
          style: const TextStyle(
              fontSize: 18,
              color: Colors.black,
              fontWeight: FontWeight.w600)),
      getSubtitle: (profile) => Text(
        'Birthday ${profile.birthMonth != null ? DateFormat('MMM d').format(DateTime(2022, profile.birthMonth!, profile.birthDay!)) : 'unknown'}',
        style:
        const TextStyle(fontSize: 12, color: Color(0xB2000000)),
      ),
    );
  }

}