import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tings/widgets/shared/conditional.dart';
import 'package:tings/widgets/shared/loading.dart';

import '../profile/data/profile.dart';
import '../shared/api.dart';
import '../shared/event_tracker.dart';
import 'friend_profile_list.dart';

class FriendFollowersListArguments {
  final Profile profile;

  FriendFollowersListArguments(this.profile);
}

class FriendFollowersList extends StatefulWidget {
  final Profile profile;

  static const String routeName = '/friend-followers';

  const FriendFollowersList({
    super.key,
    required this.profile
  });

  @override
  State<StatefulWidget> createState() => _FriendFollowersListState();
}

class _FriendFollowersListState extends State<FriendFollowersList> {
  final List<Profile> _followers = [];
  final List<Profile> _following = [];
  String? profileId;
  bool _loading = true;

  @override
  void initState() {
    EventTracker().track('View Other Profiles followers');
    init();
    super.initState();
  }

  Future init() async {
    setState(() {
      _loading = true;
    });
    await getMyProfileId();
    await getMyFollowing();
    await getFollowers();
    setState(() {
      _loading = false;
    });
  }

  Future getMyProfileId() async {
    if (mounted) {
      final response = await Api.get('/api/my_profile');
      if (response.statusCode == 200 && response.body != '') {
        var profile = Api.parse(response.bodyBytes, Profile.fromJson);
        setState(() {
          profileId = profile.id;
        });
      } else {
        throw Exception('Failed to load profile');
      }
    }
  }

  Future getMyFollowing() async {
    if (mounted) {
      var response = await Api.get('/api/following');
      if (response.statusCode == 200) {
        var results =
        Api.parseList<Profile>(response.bodyBytes, Profile.fromJson);
        setState(() {
          _following.clear();
          _following.addAll(results);
        });
      } else if (response.statusCode == 400) {
      } else {
        throw Exception('Failed to get following');
      }
    }
  }

  Future getFollowers() async {
    if (mounted) {
      setState(() {
        _loading = true;
      });
      var response = await Api.get('/api/followers/${widget.profile.id}');
      if (response.statusCode == 200) {
        var results =
        Api.parseList<Profile>(response.bodyBytes, Profile.fromJson);
        setState(() {
          _followers.clear();
          _followers.addAll(results);
        });
      } else if (response.statusCode == 400) {
      } else {
        throw Exception('Failed to get followers');
      }
      setState(() {
        _loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Image.asset('images/iconly/Light/ArrowLeft2.png',
              height: 24, width: 24),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              '${widget.profile.displayName} Followers',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 20,
                      color: Colors.black,
                      fontWeight: FontWeight.w700)),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        centerTitle: true,
      ),
      body: Conditional(
        condition: !_loading,
        alternate: const Center(
          child: Loading(loading: true,),
        ),
        child: FriendProfileList(
          following: _following,
          profiles: _followers,
          myProfileId: profileId,
          onRefresh: getMyFollowing,
        ),
      ),
    );
  }

}