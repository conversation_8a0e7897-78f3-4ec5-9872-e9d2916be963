import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class FriendsInfo extends StatelessWidget {
  final VoidCallback onClose;

  const FriendsInfo({super.key, required this.onClose});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Welcome to Connect!',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontWeight: FontWeight.w400)),
            textAlign: TextAlign.left),
        const SizedBox(
          height: 10,
        ),
        Text('Follow people you know to see their wishlists!',
            style: GoogleFonts.inter(
                textStyle: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.w700)),
            textAlign: TextAlign.left),
        SizedBox(
          height: 35,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              "images/iconly/Light/Add_User_yellow.png",
              height: 24,
              width: 24,
            ),
            const SizedBox(
              width: 16,
            ),
            Flexible(
              child: Text(
                  'Click into the search bar to find your friends and family on Tings',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w400)),
                  textAlign: TextAlign.left),
            ),
          ],
        ),
        const SizedBox(
          height: 16,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              "images/iconly/Light/3_User_yellow.png",
              height: 24,
              width: 24,
            ),
            const SizedBox(
              width: 16,
            ),
            Flexible(
              child: Text(
                  'View the activity tab to see what your friends and family are up to',
                  style: GoogleFonts.inter(
                      textStyle: const TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontWeight: FontWeight.w400)),
                  textAlign: TextAlign.left),
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.only(top: 30),
          child: Text(
              'People are adding items to their wishlist. Follow them to see what they’re adding!',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 18,
                      color: Colors.black,
                      fontWeight: FontWeight.w400)),
              textAlign: TextAlign.left),
        ),
        Padding(
          padding:
              const EdgeInsets.only(top: 40),
          child: ElevatedButton(
            onPressed: onClose,
            style: const ButtonStyle(
                padding: MaterialStatePropertyAll(
                    EdgeInsets.symmetric(horizontal: 50, vertical: 15))),
            child: Text(
              'Got it!',
              style: GoogleFonts.inter(
                  textStyle: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.w600)),
            ),
          ),
        )
      ],
    );
  }
}
