import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:tings/widgets/friends/add_friends.dart';
import 'package:tings/widgets/friends/friend_followers_list.dart';
import 'package:tings/widgets/friends/friend_following_list.dart';
import 'package:tings/widgets/home.dart';
import 'package:tings/widgets/product/egift/dialog/egift_dialog.dart';
import 'package:tings/widgets/product/egift/egift_list.dart';
import 'package:tings/widgets/product/egift/egift_preview.dart';
import 'package:tings/widgets/product/egift/egift_view.dart';
import 'package:tings/widgets/product/egift/sender_confirmation.dart';
import 'package:tings/widgets/product/parse_product.dart';
import 'package:tings/widgets/product/product_list.dart';
import 'package:tings/widgets/product/egift/utils/data_format.dart';
import 'package:tings/widgets/welcome.dart';
import 'package:tings/widgets/wishlist/calendar_event_create_form.dart';
import 'package:tings/widgets/wishlist/wishlist_create_form.dart';
import 'package:tings/widgets/wishlist/wishlist_edit_form.dart';

import 'widgets/product/parse_product_url_form.dart';
import 'widgets/product/product_view.dart';
import 'widgets/wishlist/calendar_event_edit_form.dart';
import 'widgets/wishlist/purchased_items.dart';
import 'widgets/wishlist/wishlist_items_list.dart';

Widget makeRoute(
    {required BuildContext context, String? routeName, Object? arguments}) {
  final Widget child =
      _buildRoute(context: context, routeName: routeName, arguments: arguments);
  return child;
}

Widget _buildRoute({
  required BuildContext context,
  String? routeName,
  Object? arguments,
}) {
  final user = FirebaseAuth.instance.currentUser;
  switch (routeName) {
    case Navigator.defaultRouteName:
      final latestLink = initialLink?.toString() ?? '';
      if (latestLink.isNotEmpty) {
        String lastSegment = latestLink.split('?').last;
        lastSegment = urlDecodeData(lastSegment);
        if (lastSegment.isNotEmpty) {
          final arguments = EgiftPreviewArguments.fromJsonString(lastSegment);
          Future.microtask(() => showDialog(
                context: context,
                builder: (context) => EgiftDialog(
                  arguments: arguments,
                ),
              ));
        }
      }

      return user != null ? const HomePage() : Welcome();
    case CalendarEventCreateForm.routeName:
      return const CalendarEventCreateForm();
    case ParseProductUrlForm.routeName:
      return const ParseProductUrlForm();
    case ParseProduct.routeName:
      ParseProductArguments args = arguments as ParseProductArguments;
      return ParseProduct(url: args.url, wishlist: args.wishlist);
    case CalendarEventEditForm.routeName:
      CalendarEventEditFormArguments args =
          arguments as CalendarEventEditFormArguments;
      return CalendarEventEditForm(event: args.event);
    case PurchasedItems.routeName:
      PurchasedItemsArguments args = arguments as PurchasedItemsArguments;
      return PurchasedItems(
        items: args.items,
        total: args.total,
      );
    case WishlistCreateForm.routeName:
      return const WishlistCreateForm();
    case WishlistEditForm.routeName:
      WishlistEditFormArguments args = arguments as WishlistEditFormArguments;
      return WishlistEditForm(wishlist: args.wishlist);
    case WishlistItemsList.routeName:
      WishlistItemsListArguments args = arguments as WishlistItemsListArguments;
      return WishlistItemsList(wishlist: args.wishlist, isOwner: args.isOwner);
    case ProductView.routeName:
      ProductViewArguments args = arguments as ProductViewArguments;
      return ProductView(
        product: args.product,
        wishlistItem: args.wishlistItem,
        isOwner: args.isOwner,
      );
    case EgiftView.routeName:
      EgiftViewArguments args = arguments as EgiftViewArguments;
      return EgiftView(
        product: args.product,
        wishlistItem: args.wishlistItem,
        isOwner: args.isOwner,
      );
    case EgiftPreview.routeName:
      EgiftPreviewArguments args = arguments as EgiftPreviewArguments;
      return EgiftPreview(
        arguments: args,
      );
    case SenderConfirmation.routeName:
      return const SenderConfirmation();
    case AddFriends.routeName:
      AddFriendsArguments args = arguments as AddFriendsArguments;
      return AddFriends(friends: args.friends);
    case FriendFollowersList.routeName:
      FriendFollowersListArguments args =
          arguments as FriendFollowersListArguments;
      return FriendFollowersList(
        profile: args.profile,
      );
    case FriendFollowingList.routeName:
      FriendFollowingListArguments args =
          arguments as FriendFollowingListArguments;
      return FriendFollowingList(
        profile: args.profile,
      );
    case ProductList.routeName:
      ProductListArguments args = arguments as ProductListArguments;

      var index = -1;
      if (args.titleText != null) {
        String lowerCaseTitle = args.titleText!.toLowerCase();
        int indexEgift = lowerCaseTitle.indexOf('egift');
        int indexSendATing = lowerCaseTitle.indexOf('send a ting');

        if (indexEgift >= 0) {
          index = indexEgift;
        } else if (indexSendATing >= 0) {
          index = indexSendATing;
        }
      }

      if (index == -1) {
        return ProductList(
          title: args.title,
          centerTitle: args.centerTitle,
          category: args.category,
          search: args.search,
          showBackButton: args.showBackButton,
        );
      }

      return EgiftList(
        title: args.title,
        centerTitle: args.centerTitle,
        category: args.category,
        search: args.search,
        showBackButton: args.showBackButton,
      );

    default:
      throw 'Route $routeName is not defined';
  }
}
