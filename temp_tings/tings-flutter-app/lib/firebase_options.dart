// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD7WxkhdPrZgTlCJkriFZyvX_R4T1Wow7Q',
    appId: '1:856550431904:android:bf51c0e06fd570e7ef7dc5',
    messagingSenderId: '856550431904',
    projectId: 'tings-53769',
    storageBucket: 'tings-53769.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCr2I8Z5ckpbaC8_IglUTj0ZTGwftX_CfY',
    appId: '1:856550431904:ios:0d95563e1dcbb395ef7dc5',
    messagingSenderId: '856550431904',
    projectId: 'tings-53769',
    storageBucket: 'tings-53769.appspot.com',
    androidClientId: '856550431904-7np8tbje6itvsi2757jvb4sv16hhp8b2.apps.googleusercontent.com',
    iosClientId: '856550431904-opkvee1knlfb5j8ogrpc70njnnm1njje.apps.googleusercontent.com',
    iosBundleId: 'com.tings.tingsapp',
  );
}
