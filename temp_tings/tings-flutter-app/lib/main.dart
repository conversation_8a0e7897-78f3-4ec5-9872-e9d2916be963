import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tings/blocs/bottom_bar_cubit/bottom_bar_cubit.dart';
import 'package:tings/firebase_options.dart';
import 'package:tings/theme.dart';
import 'package:tings/widgets/product/egift/dialog/egift_dialog.dart';
import 'package:tings/widgets/product/egift/utils/data_format.dart';
import 'package:uni_links/uni_links.dart';

import 'router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  try {
    initialLink = await getInitialLink();
  } catch (e) {
    // Handle error
  }
  // await FirebaseAuth.instance.useAuthEmulator('localhost', 9099);
  runApp(const App());
}

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    FocusManager.instance.primaryFocus?.unfocus();
    return MultiBlocProvider(
      providers: [
        BlocProvider<BottomBarCubit>(
          create: (BuildContext context) => BottomBarCubit(),
        ),
      ],
      child: MaterialApp(
        title: 'Tings',
        theme: tingsTheme,
        initialRoute: '/',
        onGenerateRoute: (RouteSettings settings) {
          return MaterialPageRoute(
            builder: (BuildContext context) => makeRoute(
              context: context,
              routeName: settings.name,
              arguments: settings.arguments,
            ),
            maintainState: true,
            fullscreenDialog: false,
          );
        },
      ),
    );
  }
}
