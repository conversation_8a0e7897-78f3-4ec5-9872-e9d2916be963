import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

var tingsTheme = ThemeData(
    progressIndicatorTheme:
        const ProgressIndicatorThemeData(color: Colors.black87),
    elevatedButtonTheme: const ElevatedButtonThemeData(
        style: ButtonStyle(
            shape: MaterialStatePropertyAll(RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(50)),
            )),
            backgroundColor: MaterialStatePropertyAll(Colors.black),
            minimumSize: MaterialStatePropertyAll(Size.fromHeight(54)),
            textStyle: MaterialStatePropertyAll(
                TextStyle(fontSize: 18, fontWeight: FontWeight.w600)))),
    outlinedButtonTheme: const OutlinedButtonThemeData(
        style: ButtonStyle(
            shape: MaterialStatePropertyAll(RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(50)),
            )),
            side: MaterialStatePropertyAll(
                BorderSide(color: Colors.black, width: 2)),
            minimumSize: MaterialStatePropertyAll(Size.fromHeight(54)),
            textStyle: MaterialStatePropertyAll(
                TextStyle(fontSize: 18, fontWeight: FontWeight.w600)))),
    snackBarTheme: const SnackBarThemeData(
      backgroundColor: Color(0xFFFFC107),
    ),
    // backgroundColor: const Color(0xFFFFFFFF),
    scaffoldBackgroundColor: const Color(0xFFFFFFFF),
    textTheme: GoogleFonts.interTextTheme(),
    appBarTheme: const AppBarTheme(
        elevation: 0,
        backgroundColor: Color(0xFFFFFFFF),
        shadowColor: Color(0xFFFFFF)),
    bottomSheetTheme: const BottomSheetThemeData(
      backgroundColor: Colors.transparent,
      constraints: BoxConstraints(),
    ),
    inputDecorationTheme: const InputDecorationTheme(
      border: OutlineInputBorder(
          borderSide: BorderSide(
        color: Color(0xFFCACACA),
      )),
      focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
        color: Color(0xFFCACACA),
      )),
      floatingLabelStyle: TextStyle(color: Color(0xFFCACACA)),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        showUnselectedLabels: true,
        selectedItemColor: Color(0xFF200E32),
        unselectedItemColor: Color(0xFF4D4D4D),
        unselectedLabelStyle: TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 10,
            color: Color(0xFF4D4D4D)),
        selectedLabelStyle:
            TextStyle(fontWeight: FontWeight.w500, fontSize: 10)));
